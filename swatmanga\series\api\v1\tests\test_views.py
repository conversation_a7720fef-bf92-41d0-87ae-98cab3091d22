import io
from django.contrib.auth import get_user_model
from django.contrib.auth.models import Group
from django.core.files.uploadedfile import SimpleUploadedFile
from django.urls import reverse
from PIL import Image
from rest_framework import status
from rest_framework.test import APITestCase

from ....groups import SerieGroups
from ....models import (
    Chapter,
    Genre,
    Serie,
    SerieArtist,
    SerieAuthor,
    SerieStatus,
    SerieType,
)


User = get_user_model()

import pytest


pytestmark = pytest.mark.django_db


@pytest.mark.django_db
class SerieViewSetTests(APITestCase):
    def setUp(self):
        manage_series = Group.objects.create(name=SerieGroups.MANAGE_SERIES)
        self.user = User.objects.create_user(
            password="testpass", email="<EMAIL>"
        )
        self.series_manager = User.objects.create_user(
            password="seriesmanapger", email="<EMAIL>"
        )
        self.series_manager.groups.add(manage_series)
        self.series_manager.save()
        self.admins_manager = User.objects.create_user(
            password="adminsmanapger", email="<EMAIL>", is_staff=True
        )
        self.client.force_authenticate(user=self.series_manager)

        self.author = SerieAuthor.objects.create(name="Author A")
        self.artist = SerieArtist.objects.create(name="Artist A")
        self.type = SerieType.objects.create(name="Manga")
        self.status = SerieStatus.objects.create(name="Ongoing")
        self.genre = Genre.objects.create(name="Action")

        # Create a dummy image using Pillow
        image = Image.new("RGB", (100, 100), color="red")
        image_file = io.BytesIO()
        image.save(image_file, format="JPEG")
        image_file.seek(0)
        self.cover_image = SimpleUploadedFile(
            "cover.jpg", image_file.read(), content_type="image/jpeg"
        )

        self.serie_pos_data = {
            "title": "Test Serie",
            "slug": "test-serie",
            "type_id": self.type.id,
            "status_id": self.status.id,
            "author_id": self.author.id,
            "artist_id": self.artist.id,
            "genres_ids": [self.genre.id],
            "story": "This is a test story.",
            "cover": self.cover_image,
        }
        self.serie_data = self.serie_pos_data.copy()
        self.serie_data.pop("genres_ids", None)

    def test_create_serie(self):
        url = reverse("api:v1:serie-list")
        assert not self.serie_pos_data
        response = self.client.post(url, self.serie_pos_data, format="multipart")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(Serie.objects.count(), 1)
        self.assertEqual(Serie.objects.get().title, "Test Serie")

    def test_retrieve_serie(self):
        serie = Serie.objects.create(**self.serie_data, created_by=self.user)
        url = reverse("api:v1:serie-detail", args=[serie.id])
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["title"], "Test Serie")

    def test_update_serie(self):
        serie = Serie.objects.create(**self.serie_data, created_by=self.user)
        url = reverse("api:v1:serie-detail", args=[serie.id])
        update_data = {"title": "Updated Title"}
        response = self.client.patch(url, update_data, format="json")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        serie.refresh_from_db()
        self.assertEqual(serie.title, "Updated Title")

    def test_delete_serie(self):
        serie = Serie.objects.create(**self.serie_data, created_by=self.user)
        url = reverse("api:v1:serie-detail", args=[serie.id])
        response = self.client.delete(url)
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        self.assertEqual(Serie.objects.count(), 0)


@pytest.mark.django_db
class ChapterViewSetTests(APITestCase):
    def setUp(self):
        manage_chapters = Group.objects.create(name=SerieGroups.MANAGE_CHAPTERS)
        self.user = User.objects.create_user(
            password="testpass", email="<EMAIL>"
        )
        self.chapters_manager = User.objects.create_user(
            password="chaptersmanapger", email="<EMAIL>"
        )
        self.chapters_manager.groups.add(manage_chapters)
        self.chapters_manager.save()
        self.admins_manager = User.objects.create_user(
            password="adminsmanapger", email="<EMAIL>", is_staff=True
        )
        self.client.force_authenticate(user=self.chapters_manager)

        self.serie = Serie.objects.create(
            title="Test Serie",
            slug="test-serie",
            type=SerieType.objects.create(name="Manga"),
            status=SerieStatus.objects.create(name="Ongoing"),
            author=SerieAuthor.objects.create(name="Author A"),
            artist=SerieArtist.objects.create(name="Artist A"),
            created_by=self.user,
            story="This is a test story.",
        )

        self.chapter_data = {
            "title": "Chapter 1",
            "slug": "chapter-1",
            "number": 1,
            "serie": self.serie.id,
        }

    def test_create_chapter_no_permissions(self):
        self.client.force_authenticate(self.user)

        url = reverse("api:v1:chapter-list")
        response = self.client.post(url, self.chapter_data, format="json")
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_retrieve_chapter_no_permissions(self):
        self.client.force_authenticate(self.user)

        chapter = Chapter.objects.create(
            **self.chapter_data, created_by=self.user, serie=self.serie
        )
        url = reverse("api:v1:chapter-detail", args=[chapter.id])
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_updated_chapter_no_permissions(self):
        self.client.force_authenticate(self.user)

        chapter = Chapter.objects.create(
            **self.chapter_data, created_by=self.user, serie=self.serie
        )
        url = reverse("api:v1:chapter-detail", args=[chapter.id])
        update_data = {"title": "Updated Chapter"}
        response = self.client.patch(url, update_data, format="json")
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_delete_chapter(self):
        self.client.force_authenticate(self.user)

        chapter = Chapter.objects.create(
            **self.chapter_data, created_by=self.user, serie=self.serie
        )
        url = reverse("api:v1:chapter-detail", args=[chapter.id])
        response = self.client.delete(url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_create_chapter(self):
        url = reverse("api:v1:chapter-list")
        response = self.client.post(url, self.chapter_data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(Chapter.objects.count(), 1)
        self.assertEqual(Chapter.objects.get().title, "Chapter 1")

    def test_retrieve_chapter(self):
        chapter = Chapter.objects.create(
            **self.chapter_data, created_by=self.user, serie=self.serie
        )
        url = reverse("api:v1:chapter-detail", args=[chapter.id])
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["title"], "Chapter 1")

    def test_update_chapter(self):
        chapter = Chapter.objects.create(
            **self.chapter_data, created_by=self.user, serie=self.serie
        )
        url = reverse("api:v1:chapter-detail", args=[chapter.id])
        update_data = {"title": "Updated Chapter"}
        response = self.client.patch(url, update_data, format="json")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        chapter.refresh_from_db()
        self.assertEqual(chapter.title, "Updated Chapter")

    def test_delete_chapter(self):
        chapter = Chapter.objects.create(
            **self.chapter_data, created_by=self.user, serie=self.serie
        )
        url = reverse("api:v1:chapter-detail", args=[chapter.id])
        response = self.client.delete(url)
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        self.assertEqual(Chapter.objects.count(), 0)
