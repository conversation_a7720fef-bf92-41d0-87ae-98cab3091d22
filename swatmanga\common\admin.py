from django.contrib import admin
from hitcount.admin import HitAdmin
from hitcount.models import Hit
from user_agents import parse


admin.site.unregister(Hit)


@admin.register(Hit)
class HitAdmin(HitAdmin):
    list_display = (
        "created",
        "user",
        "ip",
        "hitcount",
        "browser",
        "browser_version",
        "os",
        "os_version",
        "device",
    )
    search_fields = ("ip", "user_agent")

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

    def setup_user_agent(self, obj):
        if not hasattr(self, "user_agent") or self.user_agent_info != obj.user_agent:
            self.user_agent = parse(obj.user_agent)
            self.user_agent_info = obj.user_agent

    def device(self, obj):
        self.setup_user_agent(obj)
        return self.user_agent.device

    def browser(self, obj):
        self.setup_user_agent(obj)
        return self.user_agent.browser.family

    def browser_version(self, obj):
        self.setup_user_agent(obj)
        return self.user_agent.browser.version_string

    def os(self, obj):
        self.setup_user_agent(obj)
        os = self.user_agent.os.family
        return os

    def os_version(self, obj):
        self.setup_user_agent(obj)
        os = self.user_agent.os.version_string
        return os
