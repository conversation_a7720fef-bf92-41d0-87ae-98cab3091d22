from django.core.management.base import BaseCommand
from django_celery_beat.models import PeriodicTask, IntervalSchedule

class Command(BaseCommand):
    help = 'Initialize Celery Beat periodic tasks'

    def handle(self, *args, **options):
        # Create schedule to run daily
        schedule, _ = IntervalSchedule.objects.get_or_create(
            every=1,
            period=IntervalSchedule.DAYS,
        )

        # Create or update expiration task
        PeriodicTask.objects.update_or_create(
            name='Expire Subscriptions Daily',
            defaults={
                'task': 'swatmanga.plans.tasks.expire_subscriptions',
                'interval': schedule,
                'enabled': True,
            }
        )