# Generated by Django 5.0.8 on 2025-02-07 06:42

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="AppVersions",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("latest_version", models.Char<PERSON>ield(default="0.0.0", max_length=20)),
                ("latest_version_link", models.URLField(blank=True, default="")),
                (
                    "minimum_supported_version",
                    models.CharField(default="0.0.0", max_length=20),
                ),
                (
                    "minimum_supported_version_link",
                    models.URLField(blank=True, null=True),
                ),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
        ),
    ]
