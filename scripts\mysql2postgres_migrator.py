# TODO: user's avatars
# TODO: donations
# TODO: plans/subscriptions
# TODO: change rating type to float
# TODO: create EmailAdress entries from users and emails

import os
from pathlib import Path

import argparse
import diskcache as dc
import logging
import pandas as pd
from datetime import datetime
from sqlalchemy import create_engine, text
from sqlalchemy.dialects.postgresql import insert
from tqdm import tqdm


WRONG_DATA_DIR = Path("wrong_data")

# Set up cache
cache = dc.Cache("cache_directory")

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


def reset_auto_increment(postgres_engine, table_name, num=None):
    with postgres_engine.connect() as conn:
        if num is None:
            max_id_query = f"SELECT COALESCE(MAX(id), 0) FROM {table_name};"
            max_id = conn.execute(text(max_id_query)).scalar()
        else:
            max_id = num

        # Update the sequence to start from max_id + 1
        sequence_name = f"{table_name}_id_seq"  # Assuming the default sequence naming
        reset_sequence_query = f"SELECT setval('{sequence_name}', {max_id + 1}, false);"
        conn.execute(text(reset_sequence_query))
        logger.info(f"Reset sequence for {table_name} to {max_id + 1}.")
        conn.commit()


def filter_valid_favorites_and_follows(favorites_df, follows_df, series_df, users_df):
    # Filter favorites to only include valid series and users
    valid_serie_ids = series_df["id"].unique()
    valid_user_ids = users_df["id"].unique()

    favorites_df = favorites_df[
        (favorites_df["serie_id"].isin(valid_serie_ids))
        & (favorites_df["user_id"].isin(valid_user_ids))
    ]

    follows_df = follows_df[
        (follows_df["serie_id"].isin(valid_serie_ids))
        & (follows_df["user_id"].isin(valid_user_ids))
    ]

    return favorites_df, follows_df


def resolve_order_duplicates(chapter_images_df):
    # Sort by chapter_id, orders, and created_at for consistent ordering
    chapter_images_df = chapter_images_df.sort_values(
        by=["chapter_id", "order", "created_at", "id"]
    )

    # Assign new unique 'orders' starting from 1 for each chapter_id group
    chapter_images_df["new_orders"] = (
        chapter_images_df.groupby("chapter_id").cumcount() + 1
    )

    # Replace the old 'orders' with the new unique 'orders'
    chapter_images_df["order"] = chapter_images_df["new_orders"]

    # Drop the 'new_orders' column as it's no longer needed
    chapter_images_df = chapter_images_df.drop(columns=["new_orders"])

    return chapter_images_df


def insert_on_conflict_nothing(table, conn, keys, data_iter):
    # "a" is the primary key in "conflict_table"
    data = [dict(zip(keys, row)) for row in data_iter]
    idx_elems = ["id"]
    if table in ["favroites", "follows"]:
        idx_elems = ["serie_id", "user_id"]
    stmt = (
        insert(table.table)
        .values(data)
        .on_conflict_do_nothing(index_elements=idx_elems)
    )
    result = conn.execute(stmt)

    return result.rowcount


# Command-line argument parser
def get_args():
    parser = argparse.ArgumentParser(
        description="MySQL to PostgreSQL Data Migration Script"
    )
    parser.add_argument(
        "--mysql-url", type=str, required=True, help="MySQL connection URL"
    )
    parser.add_argument(
        "--postgres-url", type=str, required=True, help="PostgreSQL connection URL"
    )
    return parser.parse_args()


# Load data from MySQL using SQLAlchemy
def load_mysql_data(query, mysql_engine):
    # Try to load from cache first
    if query in cache:
        logger.info(f"Loading data from cache: {query}")
        return cache[query]

    # If not in cache, query the database
    logger.info(f"Loading data with query: {query}")
    data = pd.read_sql(query, mysql_engine)

    # Store the result in cache
    cache[query] = data

    return data


# Create PostgreSQL table if it does not exist and insert data
def create_and_insert_table(df, table_name, postgres_engine):
    logger.info(f"Creating table {table_name} if not exists and inserting data.")
    create_table_query = f"""
        CREATE TABLE IF NOT EXISTS {table_name} (
            id BIGINT PRIMARY KEY,
            name VARCHAR(255) UNIQUE
        );
    """
    if "id" in df:
        insert_query = f"""
            INSERT INTO {table_name} (id, name)
            VALUES (:id, :name)
            ON CONFLICT (name) DO NOTHING;
        """
        with postgres_engine.connect() as conn:
            conn.execute(text(create_table_query))
            for id, name in tqdm(
                zip(df["id"], df["name"]), desc=f"Inserting data into {table_name}"
            ):
                conn.execute(text(insert_query), {"id": id, "name": name})
            conn.commit()
    else:
        insert_query = f"""
            INSERT INTO {table_name} (name)
            VALUES (:name)
            ON CONFLICT (name) DO NOTHING;
        """
        with postgres_engine.connect() as conn:
            conn.execute(text(create_table_query))
            for name in tqdm(df["name"], desc=f"Inserting data into {table_name}"):
                conn.execute(text(insert_query), {"name": name})
            conn.commit()


def fetch_group_ids(postgres_engine, group_names):
    query = "SELECT id, name FROM auth_group WHERE name = ANY(:names)"
    group_ids = {}
    with postgres_engine.connect() as conn:
        result = conn.execute(text(query), {"names": group_names})
        for row in result:
            group_ids[row[1]] = row[0]
    return group_ids


def insert_users_into_group(users_df, group_id, postgres_engine):
    logger.info(f"Inserting users into group ID {group_id} in users_user_groups table.")
    insert_query = """
        INSERT INTO users_user_groups (user_id, group_id)
        VALUES (:user_id, :group_id)
        ON CONFLICT DO NOTHING;
    """
    with postgres_engine.connect() as conn:
        for user_id in tqdm(
            users_df["user_id"],
            desc=f"Inserting {len(users_df)} users into group ID {group_id}",
        ):
            conn.execute(text(insert_query), {"user_id": user_id, "group_id": group_id})
        conn.commit()


# Fetch IDs from PostgreSQL tables
def fetch_ids(df, table_name, postgres_engine):
    logger.info(f"Fetching IDs from {table_name}.")
    ids = {}
    query = f"SELECT id FROM {table_name} WHERE name = :name"
    with postgres_engine.connect() as conn:
        for value in tqdm(df["name"], desc=f"Fetching IDs for {table_name}"):
            result = conn.execute(text(query), {"name": value})
            ids[value] = result.scalar()
    return ids


# Define SQL queries
def get_queries():
    return {
        "upload_manga_users": """
                SELECT id AS user_id
                FROM users
                WHERE therole = 'upload_manga';
            """,
        "upload_chapter_users": """
                SELECT id AS user_id
                FROM users
                WHERE therole = 'upload_chapter';
            """,
        "users_user": """
            SELECT
                id,
                username,
                username AS name,
                email,
                password,
                status AS is_active,
                CASE
                    WHEN userlevel = 'admin' THEN 1
                    WHEN userlevel = 'user' THEN 0
                END AS is_staff,
                created_at,
                updated_at
            FROM
                users;
        """,
        "serie_authors": "SELECT DISTINCT author AS name FROM manga WHERE author IS NOT NULL AND author != '';",
        "serie_artists": "SELECT DISTINCT artist AS name FROM manga WHERE artist IS NOT NULL AND artist != '';",
        "genres": "SELECT DISTINCT name, id FROM terms WHERE type = 'genres' AND name IS NOT NULL AND name != '';",
        "serie_types": "SELECT DISTINCT manga_type AS name FROM manga WHERE manga_type IS NOT NULL AND manga_type != '';",
        "serie_statuses": "SELECT DISTINCT manga_status AS name FROM manga WHERE manga_status IS NOT NULL AND manga_status != '';",
        "series": """
            SELECT
                m.id AS id,
                m.title AS title,
                m.slug AS slug,
                m.letter AS letter,
                mdp.file AS poster,
                mdc.file AS cover,
                m.alternative AS alternative,
                m.story AS story,
                m.author AS author_name,
                m.artist AS artist_name,
                m.published AS published,
                CAST(m.views AS UNSIGNED) AS views_count,
                CASE
                    WHEN ROUND(m.score, 1) < 10 THEN ROUND(m.score, 1)
                    ELSE 10.0
                END AS rating,
                CAST(m.countrating AS UNSIGNED) AS ratings_count,
                CAST(m.donation AS UNSIGNED) AS donations_count,
                CAST(m.follows AS UNSIGNED) AS followers_count,
                CAST(m.favorites AS UNSIGNED) AS favorites_count,
                m.hot AS is_hot,
                m.created_at AS created_at,
                m.updated_at AS updated_at,
                m.manga_type AS type_id,
                m.manga_status AS status_id,
                m.comment AS allow_comments
            FROM
                manga m
            LEFT JOIN
                media mdp ON m.thumbnail = mdp.id
            LEFT JOIN
                media mdc ON m.cover = mdc.id
            WHERE
                m.status
                AND NOT m.trash
                AND slug != '';
        """,
        "favorites": "SELECT uid AS user_id, pid AS serie_id FROM favorites;",
        "follows": "SELECT uid AS user_id, pid AS serie_id FROM follow;",
        "chapters": """
            SELECT
                c.id,
                c.title,
                c.slug,
                c.chapter,
                c.created_at,
                c.updated_at,
                c.views AS views_count,
                COUNT(ci.id) AS images_count, -- Calculate images count
                c.user_id AS created_by_id,
                c.user_id AS updated_by_id,
                c.manga_id AS serie_id,
                c.comment AS allow_comments
            FROM
                chapters c
            LEFT JOIN
                chapter_images ci ON c.gtoken = ci.gtoken -- Join on gtoken to count images
            WHERE
                c.status
                AND NOT c.trash
                AND c.chapter IS NOT NULL
                AND c.manga_id IS NOT NULL
            GROUP BY
                c.id;
        """,
        "chapter_images": "SELECT id AS id, CONCAT('chapters/', utoken, '/', file) AS image, orders AS `order`, gtoken, created_at FROM chapter_images;",
        "chapter_reads": "SELECT id AS id, cid AS chapter_id, uid AS user_id FROM chapter_read;",
    }


def migrate_series_genres(mysql_engine, postgres_engine):
    logger.info("Migrating series genres...")

    # Step 1: Load the series and genres data from MySQL
    query = "SELECT id, genres FROM manga WHERE genres IS NOT NULL AND genres != '' AND status AND NOT trash AND slug != '';"
    series_genres_df = load_mysql_data(query, mysql_engine)

    # Step 2: Load the genre mapping from PostgreSQL
    genres_query = "SELECT id, name FROM genres;"
    genres_df = pd.read_sql(genres_query, postgres_engine)
    genre_id_mapping = {row["id"]: row["id"] for _, row in genres_df.iterrows()}

    # Step 3: Prepare the data for insertion into series_genres
    series_genres_insert_data = []

    for _, row in series_genres_df.iterrows():
        serie_id = row["id"]
        genre_ids = row["genres"].split(",")  # Assuming genres are comma-separated
        for genre_id in genre_ids:
            genre_id = int(
                genre_id.strip().replace("'", "").replace('"', "")
            )  # Clean up spaces
            if genre_id in genre_id_mapping:
                series_genres_insert_data.append(
                    {"serie_id": serie_id, "genre_id": genre_id_mapping[genre_id]}
                )
            else:
                logger.warning(
                    f"Genre ID {genre_id} not found in PostgreSQL genres table."
                )

    # Step 4: Insert data into series_genres table
    if series_genres_insert_data:
        series_genres_df = pd.DataFrame(series_genres_insert_data)
        series_genres_df.to_sql(
            "series_genres", postgres_engine, if_exists="append", index=False
        )
        logger.info(
            f"Inserted {len(series_genres_insert_data)} records into series_genres."
        )
    else:
        logger.warning("No valid genre mappings found.")


# Map gtoken to chapter_id
def map_gtoken_to_chapter_id(mysql_engine):
    logger.info("Mapping gtoken to chapter_id.")
    query = "SELECT gtoken, id FROM chapters;"
    df = load_mysql_data(query, mysql_engine)
    return dict(zip(df["gtoken"], df["id"]))


def process_user_emails(row):
    if not row["email"]:
        log_wrong_data(row, "users_emails.txt")
        return f"{row['username']}@emptyemail.com"
    else:
        return row["email"]


def log_wrong_data(row, file):
    with open(WRONG_DATA_DIR / file, "a", encoding="utf8") as file:
        if hasattr(row, "to_dict"):
            log_entry = str(row.to_dict())
        else:
            log_entry = str(row)
        file.write(log_entry)


def main():
    args = get_args()

    # Initialize engines
    mysql_engine = create_engine(args.mysql_url, connect_args={"client_flag": 0})
    postgres_engine = create_engine(args.postgres_url)

    os.makedirs(WRONG_DATA_DIR, exist_ok=True)

    # Load and process distinct values
    queries = get_queries()
    distinct_values = {
        table: load_mysql_data(query, mysql_engine)
        for table, query in queries.items()
        if table
        in [
            "serie_authors",
            "serie_artists",
            "serie_types",
            "serie_statuses",
            "genres",
        ]
    }

    # Deleting old data from PostgreSQL
    pbar_desc = "Deleting data from %s"
    pbar = tqdm(distinct_values.keys(), desc=pbar_desc % "PostgreSQL")
    with postgres_engine.connect() as conn:
        for table_name in pbar:
            pbar.set_description(pbar_desc % table_name)
            conn.execute(text(f"truncate {table_name} CASCADE;"))
        conn.commit()

    # Create tables and insert distinct values into PostgreSQL
    for table_name, df in distinct_values.items():
        create_and_insert_table(df, table_name, postgres_engine)
        reset_auto_increment(postgres_engine, table_name)

    # Fetch IDs for authors, artists, and manga_types
    ids = {
        table_name: fetch_ids(df, table_name, postgres_engine)
        for table_name, df in distinct_values.items()
    }

    # Load and prepare data from MySQL
    mysql_data = {
        table: load_mysql_data(query, mysql_engine)
        for table, query in queries.items()
        if table
        in [
            "users_user",
            "series",
            "favorites",
            "follows",
            "chapters",
            "chapter_images",
            "chapter_reads",
        ]
    }

    # Deleting old data from PostgreSQL
    pbar_desc = "Deleting data from %s"
    pbar = tqdm(mysql_data.keys(), desc=pbar_desc % "PostgreSQL")
    with postgres_engine.connect() as conn:
        for table_name in pbar:
            pbar.set_description(pbar_desc % table_name)
            conn.execute(text(f"truncate {table_name} CASCADE;"))
        conn.commit()

    # Process 'users' DataFrame
    users_df = mysql_data["users_user"]
    users_df["is_active"] = users_df["is_active"].map(lambda x: bool(x))
    users_df["is_staff"] = users_df["is_staff"].map(lambda x: bool(x))
    users_df["is_superuser"] = users_df["id"].map(lambda x: False)
    # Replace empty emails with {username}@emptyemail.com
    users_df["email"] = users_df.apply(
        process_user_emails,
        axis=1,
    )
    # Sort by 'created_at' in descending order to ensure the most recent entry is first
    users_df = users_df.sort_values(by="created_at", ascending=False)

    # Remove duplicate usernames, keeping the most recent (first occurrence after sorting)
    users_df = users_df.drop_duplicates(subset=["username"], keep="first")
    # TODO: should query real coins

    # Remove duplicate emails, keeping the most recent (first occurrence after sorting)
    users_df = users_df.drop_duplicates(subset=["email"], keep="first")

    users_df["password"] = users_df["password"].apply(
        lambda x: f"bcrypt${x}" if x.startswith("$2y$") else x
    )

    mysql_data["users_user"] = users_df

    # Process 'series' DataFrame
    series_df = mysql_data["series"]
    series_df["author_id"] = series_df["author_name"].map(ids["serie_authors"])
    series_df["artist_id"] = series_df["artist_name"].map(ids["serie_artists"])
    series_df["type_id"] = series_df["type_id"].map(ids["serie_types"])
    series_df["status_id"] = series_df["status_id"].map(ids["serie_statuses"])
    series_df["is_hot"] = series_df["is_hot"].map(lambda x: bool(x))
    series_df["published"] = pd.to_datetime(
        series_df["published"], errors="coerce"
    ).dt.date
    series_df["allow_comments"] = series_df["allow_comments"].map(lambda x: True)
    # Count chapters by series ID (serie_id)
    chapters_count_df = (
        mysql_data["chapters"]
        .groupby("serie_id")
        .size()
        .reset_index(name="chapters_count")
    )
    # Merge chapters count into series_df
    series_df = series_df.merge(
        chapters_count_df, left_on="id", right_on="serie_id", how="left"
    )

    # Fill NaN values for series with no chapters
    series_df["chapters_count"] = series_df["chapters_count"].fillna(0).astype(int)
    series_df = series_df.drop(columns=["author_name", "artist_name", "serie_id"])
    mysql_data["series"] = series_df

    # Process 'chapters' DataFrame
    chapters_df = mysql_data["chapters"]

    # Filter chapters to only include valid series
    chapters_df = chapters_df[(chapters_df["serie_id"].isin(series_df["id"].unique()))]
    chapters_df["allow_comments"] = chapters_df["allow_comments"].map(lambda x: True)
    chapters_df = chapters_df.dropna(subset=["chapter"])
    chapters_df = chapters_df.sort_values(by="created_at", ascending=False)
    chapters_df_duplicates = chapters_df[
        chapters_df.duplicated(subset=["serie_id", "chapter"], keep="last") == True
    ]
    for row in chapters_df_duplicates.iterrows():
        log_wrong_data(row, "chapters_duplicate.txt")

    chapters_df = chapters_df.drop_duplicates(
        subset=["serie_id", "chapter"], keep="last"
    )
    mysql_data["chapters"] = chapters_df

    # Process 'chapter_images' DataFrame
    if "chapter_images" in mysql_data:
        gtoken_to_chapter_id = map_gtoken_to_chapter_id(mysql_engine)
        chapter_images_df = mysql_data["chapter_images"]
        chapter_images_df["updated_at"] = chapter_images_df["id"].map(lambda x: pd.NaT)
        chapter_images_df["chapter_id"] = chapter_images_df["gtoken"].map(
            gtoken_to_chapter_id
        )
        chapter_images_df = chapter_images_df[
            chapter_images_df["chapter_id"].isin(chapters_df["id"].unique())
        ]
        chapter_images_df = chapter_images_df.dropna(subset=["chapter_id"])
        chapter_images_df = resolve_order_duplicates(chapter_images_df)
        chapter_images_df = chapter_images_df.drop(columns=["gtoken"])
        mysql_data["chapter_images"] = chapter_images_df

    # Process 'favorites' and 'follows' DataFrames
    if "favorites" in mysql_data and "follows" in mysql_data:
        favorites_df = mysql_data["favorites"]
        follows_df = mysql_data["follows"]

        # Filter invalid series and users
        favorites_df, follows_df = filter_valid_favorites_and_follows(
            favorites_df, follows_df, series_df, users_df
        )
        favorites_df = favorites_df.drop_duplicates(
            subset=["serie_id", "user_id"], keep="first"
        )
        follows_df = follows_df.drop_duplicates(
            subset=["serie_id", "user_id"], keep="first"
        )

        mysql_data["favorites"] = favorites_df
        mysql_data["follows"] = follows_df

    if "chapter_reads" in mysql_data:
        chapter_reads_df = mysql_data["chapter_reads"]
        chapter_reads_df["created_at"] = chapter_reads_df["id"].map(
            lambda x: datetime.now()
        )
        chapter_reads_df = chapter_reads_df[
            chapter_reads_df["chapter_id"].isin(chapters_df["id"].unique())
        ]
        chapter_reads_df = chapter_reads_df[
            chapter_reads_df["user_id"].isin(users_df["id"].unique())
        ]
        chapter_reads_df = chapter_reads_df.drop_duplicates(
            subset=["chapter_id", "user_id"], keep="last"
        )
        mysql_data["chapter_reads"] = chapter_reads_df

    # Insert data into PostgreSQL
    pbar_desc = "Inserting data into %s"
    pbar = tqdm(mysql_data.items(), desc=pbar_desc % "PostgreSQL")
    error = None
    for table_name, df in pbar:
        pbar.set_description(pbar_desc % table_name)
        try:
            df.to_sql(
                table_name,
                postgres_engine,
                if_exists="append",
                index=False,
                method=insert_on_conflict_nothing,
                chunksize=5000,
            )

            reset_auto_increment(postgres_engine, table_name)
        except Exception as e:
            error = e._message()
            break

    if error is not None:
        logger.error(error)
        exit(1)
    else:

        # Migrate series genres
        migrate_series_genres(mysql_engine, postgres_engine)

        # Load users with therole 'upload_manga' and 'upload_chapter' from MySQL
        upload_manga_users_df = load_mysql_data(
            queries["upload_manga_users"], mysql_engine
        )
        upload_chapter_users_df = load_mysql_data(
            queries["upload_chapter_users"], mysql_engine
        )

        # Fetch group IDs for 'upload_manga' and 'upload_chapter' from PostgreSQL
        group_ids = fetch_group_ids(
            postgres_engine, ["upload_series", "upload_chapters"]
        )

        # Insert 'upload_manga' users into the 'upload_manga' group
        if "upload_series" in group_ids:
            insert_users_into_group(
                upload_manga_users_df, group_ids["upload_series"], postgres_engine
            )
        else:
            logger.error("Group 'upload_manga' not found in auth_group table.")

        # Insert 'upload_chapter' users into the 'upload_chapter' group
        if "upload_chapters" in group_ids:
            insert_users_into_group(
                upload_chapter_users_df, group_ids["upload_chapters"], postgres_engine
            )
        else:
            logger.error("Group 'upload_chapter' not found in auth_group table.")

        logger.info("Migration completed successfully.")


if __name__ == "__main__":
    main()
