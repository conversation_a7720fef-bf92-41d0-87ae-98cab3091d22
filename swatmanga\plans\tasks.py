from celery import shared_task
from django.utils import timezone
from .models import PlanSubscription, Plan

@shared_task
def expire_subscriptions():
    now = timezone.now()
    expired = PlanSubscription.objects.filter(
        plan__plan_type=Plan.PlanType.SUBSCRIPTION,
        expiration_date__lte=now,
        status=PlanSubscription.PlanSubscriptionStatus.SUBSCRIBED
    ).update(status=PlanSubscription.PlanSubscriptionStatus.EXPIRED)
    return f"Expired {expired} subscriptions"