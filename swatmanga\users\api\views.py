from drf_spectacular.utils import extend_schema, extend_schema_view
from rest_framework import status
from rest_framework.decorators import action
from rest_framework.mixins import ListModelMixin, RetrieveModelMixin, UpdateModelMixin
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.viewsets import GenericViewSet
from rest_framework.exceptions import PermissionDenied
from django.core.cache import cache
from django.db.models.signals import post_save
from django.dispatch import receiver

from swatmanga.users.models import User

from .serializers import UserDetailsSerializer, UserSerializer, UserWriteSerializer
from swatmanga.common.api.mixins import GetSerializerClassMixin, GetPermissionsMixin


@extend_schema_view(me=extend_schema(responses={200: UserDetailsSerializer, 404: None}))
class UserViewSet(GetSerializerClassMixin, RetrieveModelMixin, UpdateModelMixin, GenericViewSet):
    serializer_class = UserSerializer
    permission_classes = [IsAuthenticated]
    queryset = User.objects.all()
    lookup_field = "pk"
    serializer_action_classes = {
        "update": UserWriteSerializer,
        "partial_update": UserWriteSerializer,
        "me": UserDetailsSerializer,
        "retrieve": UserDetailsSerializer,
    }


    def get_queryset(self, *args, **kwargs):
        assert isinstance(self.request.user.id, int)
        return self.queryset.filter(id=self.request.user.id)

    def update(self, request, *args, **kwargs):
        """users can update their own data only"""
        user = self.get_object()
        if user != request.user:
            raise PermissionDenied()
        return super().update(request, *args, **kwargs)

    def partial_update(self, request, *args, **kwargs):
        """users can update their own data only"""
        user = self.get_object()
        if user != request.user:
            raise PermissionDenied()
        return super().partial_update(request, *args, **kwargs)

    @action(detail=False)
    def me(self, request):
        user = request.user
        cache_key = f"user_me_{user.id}"
        cached_data = cache.get(cache_key)
        
        if cached_data is not None:
            return Response(cached_data, status=status.HTTP_200_OK)
        
        serializer = self.get_serializer(user, context={"request": request})
        data = serializer.data
        cache.set(cache_key, data, timeout=60 * 15)  # 15 minutes
        return Response(data, status=status.HTTP_200_OK)
