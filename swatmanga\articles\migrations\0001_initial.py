# Generated by Django 5.0.8 on 2025-02-27 06:17

import django.core.validators
import django.db.models.deletion
import swatmanga.articles.models
import swatmanga.common.fields
import taggit.managers
import tinymce.models
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        (
            "taggit",
            "0006_rename_taggeditem_content_type_object_id_taggit_tagg_content_8fc721_idx",
        ),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="Article",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "slug",
                    models.SlugField(auto_created=True, max_length=255, unique=True),
                ),
                ("title", models.CharField(max_length=255)),
                ("content", tinymce.models.HTMLField()),
                (
                    "cover",
                    swatmanga.common.fields.WebPVersatileImageField(
                        upload_to=swatmanga.articles.models.article_cover_upload_to,
                        validators=[
                            django.core.validators.FileExtensionValidator(
                                allowed_extensions=["jpeg", "jpg", "webp", "png", "gif"]
                            )
                        ],
                        verbose_name="Cover Image",
                    ),
                ),
                ("is_hot", models.BooleanField(db_default=False, default=False)),
                ("public", models.BooleanField(db_default=True, default=True)),
                (
                    "allow_comments",
                    models.BooleanField(default=True, verbose_name="allow comments"),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True, null=True)),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="created_articles",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "tags",
                    taggit.managers.TaggableManager(
                        help_text="A comma-separated list of tags.",
                        through="taggit.TaggedItem",
                        to="taggit.Tag",
                        verbose_name="Tags",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="edited_articles",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
    ]
