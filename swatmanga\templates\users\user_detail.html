{% extends "base.html" %}

{% block title %}
  Profile: {{ user.name }}
{% endblock title %}

{% block content %}
  <div class="container">
    <div class="flex flex-col items-center">
      <div class="w-full">
          <h2>Profile Details</h2>
      </div>

      <!-- Avatar Display -->
      <div class="relative mb-4">
        {% if user.avatar %}
          <img src="{{ user.avatar.url }}" alt="{{ user.name }}'s Avatar" class="w-24 h-24 rounded-full object-cover border-2 border-[var(--anime-primary)] shadow-[0_0_10px_var(--anime-primary)]">
        {% else %}
          <div class="w-24 h-24 rounded-full bg-[var(--header-footer-bg)] flex items-center justify-center text-[var(--bs-gray-500)] border-2 border-[var(--anime-primary)] shadow-[0_0_10px_var(--anime-primary)]">
            No Avatar
          </div>
        {% endif %}
      </div>

      <div class="w-full max-w-md mt-6">
        <div class="space-y-4">

          <div class="flex justify-between items-center pb-2 border-b border-[rgba(0,196,255,0.2)]">
            <span class="text-[var(--bs-gray-400)] font-medium">Name</span>
            <span class="text-[var(--anime-primary)] font-semibold">{{ user.name }}</span>
          </div>

          <div class="flex justify-between items-center pb-2 border-b border-[rgba(0,196,255,0.2)]">
            <span class="text-[var(--bs-gray-400)] font-medium">Username</span>
            <span class="text-[var(--anime-primary)] font-semibold">{{ user.username }}</span>
          </div>

          <div class="flex justify-between items-center pb-2 border-b border-[rgba(0,196,255,0.2)]">
            <span class="text-[var(--bs-gray-400)] font-medium">Email</span>
            <span class="text-[var(--anime-primary)] font-semibold">{{ user.email }}</span>
          </div>

          <div class="flex justify-between items-center pb-2 border-b border-[rgba(0,196,255,0.2)]">
            <span class="text-[var(--bs-gray-400)] font-medium">Coins</span>
            <span class="text-[var(--anime-primary)] font-semibold">{{ user.coins }}</span>
          </div>

          <div class="flex justify-between items-center pb-2 border-b border-[rgba(0,196,255,0.2)]">
            <span class="text-[var(--bs-gray-400)] font-medium">Joined</span>
            <span class="text-[var(--bs-gray-200)]">{{ user.created_at|date:"F d, Y" }}</span>
          </div>

        </div>
      </div>
    </div>
        <!-- Action buttons -->
        <div class="flex flex-wrap justify-center gap-4 mt-6">
          <a href="{% url 'users:profile' %}" class="custom-btn">Edit</a>
        </div>
  </div>
{% endblock content %}