from django.core.management.base import BaseCommand

from swatmanga.users.models import User


class Command(BaseCommand):

    def handle(self, *args, **options):
        self.create_admin()
        self.create_user()

    def create_admin(self):
        username = "admin"
        email = "<EMAIL>"
        password = "admin"
        if User.objects.filter(email=email, username=username).exists():
            return
        admin = User.objects.create_superuser(
            email=email, username=username, password=password
        )
        admin.is_active = True
        admin.is_admin = True
        admin.save()

    def create_user(self):
        username = "user"
        email = "<EMAIL>"
        password = "user"
        if User.objects.filter(email=email, username=username).exists():
            return
        user = User.objects.create_user(
            email=email, username=username, password=password
        )
        user.save()
