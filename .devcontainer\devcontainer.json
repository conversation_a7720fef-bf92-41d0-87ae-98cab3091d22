// For format details, see https://containers.dev/implementors/json_reference/
{
    "name": "swatmanga_dev",
    "dockerComposeFile": [
        "../docker-compose.local.yml"
    ],
    "init": true,
    "mounts": [
        {
            "source": "./.devcontainer/bash_history",
            "target": "/home/<USER>/.bash_history",
            "type": "bind"
        },
        {
            "source": "~/.ssh",
            "target": "/home/<USER>/.ssh",
            "type": "bind"
        }
    ],
    // Tells devcontainer.json supporting services / tools whether they should run
    // /bin/sh -c "while sleep 1000; do :; done" when starting the container instead of the container’s default command
    "overrideCommand": false,
    "service": "django",
    // "remoteEnv": {"PATH": "/home/<USER>/.local/bin:${containerEnv:PATH}"},
    "remoteUser": "dev-user",
    "workspaceFolder": "/app",
    // Set *default* container specific settings.json values on container create.
    "customizations": {
    },
    // Uncomment the next line if you want start specific services in your Docker Compose config.
    // "runServices": [],
    // Uncomment the next line if you want to keep your containers running after VS Code shuts down.
    // "shutdownAction": "none",
    // Uncomment the next line to run commands after the container is created.
    "postCreateCommand": "cat .devcontainer/bashrc.override.sh >> ~/.bashrc"
}
