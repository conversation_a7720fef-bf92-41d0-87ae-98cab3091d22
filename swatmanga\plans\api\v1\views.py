from django_filters.rest_framework import DjangoFilterBackend
from drf_spectacular.utils import extend_schema
from rest_framework.decorators import action
from rest_framework.permissions import IsAdminUser
from rest_framework.response import Response
from rest_framework.viewsets import ModelViewSet, ReadOnlyModelViewSet
from django.utils.decorators import method_decorator
from django.views.decorators.cache import cache_page

from swatmanga.common.api.mixins import GetPermissionsMixin
from swatmanga.common.api.permissions import (
    IsAuthenticatedNotBlackListed,
    NotBlackListed,
)
from swatmanga.plans.api.v1.filters import PlanSubscriptionFilterSet
from swatmanga.plans.api.v1.serializers import (
    PaymentMethodSerializer,
    PlanSerializer,
    PlanSubscriptionSerializer,
    UserSubscriptionSerializer,
)
from swatmanga.plans.models import PaymentMethod, Plan, PlanSubscription


class PlanFilterBackend(DjangoFilterBackend):
    def get_filterset_class(self, view, queryset=None):
        if view.action == "my_subscriptions":
            return PlanSubscriptionFilterSet
        return None

@method_decorator(cache_page(60 * 60 * 24, key_prefix="plans.list"), name='list')
@method_decorator(cache_page(60 * 60 * 24, key_prefix="plans.detail"), name='retrieve')
class PlanViewSet(GetPermissionsMixin, ReadOnlyModelViewSet):
    queryset = Plan.objects.all()
    lookup_field = "pk"
    serializer_class = PlanSerializer
    filter_backends = [PlanFilterBackend]
    permission_classes = [IsAdminUser]
    permission_action_classes = {
        "list": [NotBlackListed],
        "retrieve": [NotBlackListed],
        "subscribe": [IsAuthenticatedNotBlackListed],
    }
    pagination_class = None

    @extend_schema(
        request={"multipart/form-data": PlanSubscriptionSerializer},
        responses={201: PlanSubscriptionSerializer},
    )
    @action(methods=["POST"], detail=True)
    def subscribe(self, request, pk: int = None):
        """Request to subscribe to a plan"""

        # TODO v2: params could have their on field params: {}
        params = {k: v for k, v in request.data.items() if k not in ["payment_receipt"]}
        serializer = PlanSubscriptionSerializer(
            data=request.data,
            context={
                "user": request.user,
                "plan": self.get_object(),
                "params": params,
                "request": request,
            },
        )
        serializer.is_valid(raise_exception=True)
        serializer.save()

        return Response(serializer.data)

    @extend_schema(
        responses={200: UserSubscriptionSerializer(many=True)},
    )
    @action(methods=["GET"], detail=False)
    def my_subscriptions(self, request):
        """Retrieves user's plans"""

        queryset = PlanSubscription.objects.filter(user=request.user)
        queryset = self.filter_queryset(queryset)
        serializer = UserSubscriptionSerializer(
            queryset, many=True, context={"request": request}
        )
        return Response(serializer.data)


class PaymentMethodViewSet(ReadOnlyModelViewSet):
    queryset = PaymentMethod.objects.prefetch_related("details").all()
    serializer_class = PaymentMethodSerializer
