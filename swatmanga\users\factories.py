import factory
from django.core.files.base import ContentFile
from django.utils import timezone
from factory.django import DjangoModelFactory, ImageField

from swatmanga.users.models import User


class UserFactory(DjangoModelFactory):
    class Meta:
        model = User
        django_get_or_create = ("username", "email")

    username = factory.Faker("user_name")
    name = factory.Faker("name")
    email = factory.Faker("email")
    avatar = ImageField(filename="avatar.webp", from_path="samples/sample_avatar.webp")
    coins = factory.Faker("random_int", min=0, max=50)
    created_at = factory.LazyFunction(timezone.now)
    updated_at = factory.LazyFunction(timezone.now)

    @factory.post_generation
    def set_password(self, create, extracted, **kwargs):
        if not create:
            return
        password = extracted if extracted else "defaultpassword"
        self.set_password(password)
        self.save()
