# api/views.py
from drf_spectacular.utils import (
    extend_schema,
    OpenApiResponse,
    OpenApiExample,
    inline_serializer,
)
from rest_framework import viewsets, status, serializers
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import AllowAny, IsAuthenticated
from rest_framework_simplejwt.tokens import RefreshToken

from allauth.account.forms import Signup<PERSON><PERSON>, LoginForm
from allauth.account.forms import (
    ResetPasswordForm,
    ResetPasswordKeyForm,
    ChangePasswordForm,
)
from allauth.account import app_settings as allauth_settings
from allauth.account.models import (
    EmailConfirmation,
    EmailConfirmationHMAC,
    EmailAddress,
)
from allauth.account.utils import send_email_confirmation
from django.contrib.auth import get_user_model

User = get_user_model()


def user_is_verified(user):
    return EmailAddress.objects.is_verified(user.email)


from .serializers import (
    LoginResponseSerializer,
    PasswordChangeSerializer,
    PasswordResetConfirmSerializer,
    PasswordResetRequestSerializer,
    RegistrationRequestSerializer,
    LoginRequestSerializer,
    ResendVerificationSerializer,
    TokenResponseSerializer,
    MessageResponseSerializer,
    ErrorResponseSerializer,
    EmailVerificationSerializer,
)


class AuthViewSet(viewsets.GenericViewSet):
    permission_classes = [AllowAny]
    serializer_classes = {
        "register": RegistrationRequestSerializer,
        "login": LoginRequestSerializer,
        "resend_verification": ResendVerificationSerializer,
        "password_change": PasswordChangeSerializer,
        "password_reset_request": PasswordResetRequestSerializer,
        "password_reset_confirm": PasswordResetConfirmSerializer,
    }

    def get_serializer_class(self):
        return self.serializer_classes.get(self.action)

    @extend_schema(
        request=RegistrationRequestSerializer,
        responses={
            201: OpenApiResponse(
                response=LoginResponseSerializer,
                description="Successfully registered and logged in (if email verification not required)",
            ),
            400: OpenApiResponse(
                response=ErrorResponseSerializer, description="Validation errors"
            ),
        },
    )
    @action(detail=False, methods=["post"], url_path="register")
    def register(self, request):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        form = SignupForm(serializer.validated_data)
        if form.is_valid():
            try:
                user = form.save(request)
            except ValueError as e:
                return Response(
                    {"errors": f"invalid {e}"}, status=status.HTTP_400_BAD_REQUEST
                )

            if (
                allauth_settings.EMAIL_VERIFICATION
                != allauth_settings.EmailVerificationMethod.MANDATORY
            ):
                refresh = RefreshToken.for_user(user)
                return Response(
                    LoginResponseSerializer(
                    {
                        "token": {
                            "refresh": str(refresh),
                            "access": str(refresh.access_token),
                        },
                        "user": user,
                    }
                ).data,
                    status=status.HTTP_201_CREATED,
                )

            return Response(
                MessageResponseSerializer({"detail": "Verification email sent"}).data,
                status=status.HTTP_201_CREATED,
            )
        return Response(
            ErrorResponseSerializer({"errors": form.errors}).data,
            status=status.HTTP_400_BAD_REQUEST,
        )

    @extend_schema(
        request=LoginRequestSerializer,
        responses={
            200: OpenApiResponse(
                response=LoginResponseSerializer, description="Successfully logged in"
            ),
            400: OpenApiResponse(
                response=ErrorResponseSerializer, description="Validation errors"
            ),
            401: OpenApiResponse(
                response=MessageResponseSerializer,
                description="Email not verified or account inactive",
            ),
        },
    )
    @action(detail=False, methods=["post"], url_path="login")
    def login(self, request):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        form = LoginForm(serializer.validated_data, request=request)
        if form.is_valid():
            user = form.user

            if (
                not user_is_verified(user)
                and allauth_settings.EMAIL_VERIFICATION == "mandatory"
            ):
                return Response(
                    MessageResponseSerializer({"detail": "Email not verified"}).data,
                    status=status.HTTP_401_UNAUTHORIZED,
                )

            refresh = RefreshToken.for_user(user)
            return Response(
                LoginResponseSerializer(
                    {
                        "token": {
                            "refresh": str(refresh),
                            "access": str(refresh.access_token),
                        },
                        "user": user,
                    }
                ).data
            )
        return Response(
            ErrorResponseSerializer({"errors": form.errors}).data,
            status=status.HTTP_400_BAD_REQUEST,
        )

    @extend_schema(
        request=inline_serializer(
            name="LogoutRequestSerializer",
            fields={"refresh": serializers.CharField(required=True)},
        ),
        responses={
            205: OpenApiResponse(description="Successfully logged out"),
            400: OpenApiResponse(
                response=MessageResponseSerializer, description="Invalid token"
            ),
        },
    )
    @action(detail=False, methods=["post"], permission_classes=[IsAuthenticated])
    def logout(self, request):
        try:
            refresh_token = request.data["refresh"]
            token = RefreshToken(refresh_token)
            token.blacklist()
            return Response(status=status.HTTP_205_RESET_CONTENT)
        except Exception as e:
            return Response(
                MessageResponseSerializer({"detail": "Invalid token"}).data,
                status=status.HTTP_400_BAD_REQUEST,
            )

    @extend_schema(
        request=ResendVerificationSerializer,
        responses={
            200: OpenApiResponse(
                response=MessageResponseSerializer,
                description="Verification email resent if account exists",
            ),
            400: OpenApiResponse(
                response=ErrorResponseSerializer, description="Validation errors"
            ),
        },
    )
    @action(detail=False, methods=["post"], url_path="resend-verification")
    def resend_verification(self, request):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        email = serializer.validated_data["email"]
        try:
            user = User.objects.get(email=email)
            if user_is_verified(user):
                return Response(
                    MessageResponseSerializer(
                        {"detail": "Email is already verified"}
                    ).data,
                    status=status.HTTP_400_BAD_REQUEST,
                )

            send_email_confirmation(request, user)
            return Response(
                MessageResponseSerializer({"detail": "Verification email resent"}).data
            )
        except User.DoesNotExist:
            # Don't reveal if user exists
            return Response(
                MessageResponseSerializer(
                    {
                        "detail": "If an account exists, a verification email has been sent"
                    }
                ).data
            )

    @extend_schema(
        request=PasswordChangeSerializer,
        responses={
            200: OpenApiResponse(
                response=MessageResponseSerializer,
                description="Password changed successfully",
            ),
            400: OpenApiResponse(
                response=ErrorResponseSerializer, description="Validation errors"
            ),
        },
    )
    @action(detail=False, methods=["post"], permission_classes=[IsAuthenticated], url_path="password-change")
    def password_change(self, request):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        form = ChangePasswordForm(user=request.user, data=serializer.validated_data)
        if form.is_valid():
            form.save()
            return Response(
                MessageResponseSerializer(
                    {"detail": "Password changed successfully"}
                ).data
            )
        return Response(
            ErrorResponseSerializer({"errors": form.errors}).data,
            status=status.HTTP_400_BAD_REQUEST,
        )

    @extend_schema(
        request=PasswordResetRequestSerializer,
        responses={
            200: OpenApiResponse(
                response=MessageResponseSerializer,
                description="Password reset email sent if account exists",
            ),
            400: OpenApiResponse(
                response=ErrorResponseSerializer, description="Validation errors"
            ),
        },
    )
    @action(detail=False, methods=["post"], url_path="password-reset/request")
    def password_reset_request(self, request):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        form = ResetPasswordForm(data=serializer.validated_data)
        if form.is_valid():
            form.save(request=request)
            return Response(
                MessageResponseSerializer(
                    {"detail": "Password reset email sent if account exists"}
                ).data
            )
        return Response(
            ErrorResponseSerializer({"errors": form.errors}).data,
            status=status.HTTP_400_BAD_REQUEST,
        )

    @extend_schema(
        request=PasswordResetConfirmSerializer,
        responses={
            200: OpenApiResponse(
                response=MessageResponseSerializer,
                description="Password reset successfully",
            ),
            400: OpenApiResponse(
                response=ErrorResponseSerializer, description="Validation errors"
            ),
        },
    )
    # @action(detail=False, methods=['post'], url_path='password-reset/confirm')
    def password_reset_confirm(self, request):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        form = ResetPasswordKeyForm(data=serializer.validated_data, user=request.user)
        if form.is_valid():
            form.save()
            return Response(
                MessageResponseSerializer(
                    {"detail": "Password reset successfully"}
                ).data
            )
        return Response(
            ErrorResponseSerializer({"errors": form.errors}).data,
            status=status.HTTP_400_BAD_REQUEST,
        )


class EmailVerificationViewSet(viewsets.GenericViewSet):
    permission_classes = [AllowAny]
    serializer_class = EmailVerificationSerializer
    lookup_field = "key"
    lookup_value_regex = "[^/]+"

    @extend_schema(
        responses={
            200: OpenApiResponse(
                response=MessageResponseSerializer,
                description="Email successfully verified",
            ),
            404: OpenApiResponse(
                response=MessageResponseSerializer,
                description="Invalid verification key",
            ),
        }
    )
    def retrieve(self, request, *args, **kwargs):
        key = kwargs["key"]
        try:
            confirmation = EmailConfirmationHMAC.from_key(key)
            if not confirmation:
                confirmation = EmailConfirmation.objects.get(key=key.lower())

            confirmation.confirm(request)
            return Response(
                MessageResponseSerializer(
                    {"detail": "Email successfully verified"}
                ).data
            )

        except EmailConfirmation.DoesNotExist:
            return Response(
                MessageResponseSerializer({"detail": "Invalid verification key"}).data,
                status=status.HTTP_404_NOT_FOUND,
            )
