from django.db.models import Case, Count, Exists, OuterRef, Prefetch, Q, Value, When
from django.utils.encoding import force_str
from django.utils.translation import gettext_lazy as _
from django_filters.rest_framework import DjangoFilterBackend
from drf_spectacular.utils import OpenApiParameter, extend_schema, extend_schema_view
from hitcount.models import HitCount
from hitcount.views import HitCountMixin
from rest_framework import status
from rest_framework.decorators import action
from rest_framework.permissions import IsAdminUser
from rest_framework.response import Response
from rest_framework.viewsets import ModelViewSet, ReadOnlyModelViewSet
from django.core.cache import cache
from django.utils.decorators import method_decorator
from django.views.decorators.cache import cache_page

from swatmanga.common.api.mixins import GetPermissionsMixin, GetSerializerClassMixin
from swatmanga.common.utils import make_cache_key_user_unique, generate_cache_key
from swatmanga.common.api.permissions import (
    IsAuthenticatedNotBlackListed,
    NotBlackListed,
)
from swatmanga.series.models import Chapter

from ...models import (
    ChapterRead,
    Favorite,
    Follow,
    Genre,
    Release,
    Serie,
    SerieArtist,
    SerieAuthor,
    SerieStatus,
    SerieType,
)
from .filters import ChapterFilterSet, GenreFilterSet, SerieArtistFilterSet, SerieAuthorFilterSet, SerieFilterSet
from .pagination import ChapterCursorPagination, NoCountPageNumberPagination
from .permissions import ManageSeriesPermission, ManageChaptersPermission, UploadChaptersPermission, UploadSeriesPermission
from .serializers import (
    ChapterDetailSerializer,
    ChapterImageSerializer,
    ChapterSerializer,
    ChapterWithSerieSerializer,
    ChapterWriteSerializer,
    DonationActionSerializer,
    GeneralActionResponse,
    GenreSerializer,
    RatingActionSerializer,
    ReleaseSchemaSerializer,
    ReleaseSerializer,
    SerieArtistSerializer,
    SerieAuthorSerializer,
    SerieDetailSerializer,
    SerieWriteSerializer,
    SerieSerializer,
    SerieStatusSerializer,
    SerieTypeSerializer,
)


RELEASES_CHAPTERS_NUMBER = 4


def create_response(details: str) -> dict:
    serializer = GeneralActionResponse(data={"details": force_str(_(details))})
    serializer.is_valid(raise_exception=True)
    return serializer.data


class AlwaysTrueContainer:
    def __contains__(self, item):
        return True


class ActionMixin:
    # Mixin to handle common actions like favorite, unfavorite, follow, unfollow, etc.

    def perform_action(
        self, request, obj, model, success_message, failure_message, create=True
    ):
        instance, created = (
            model.objects.get_or_create(**obj, defaults=obj)
            if create
            else model.objects.filter(**obj).delete()
        )
        if not created and create:
            return Response(
                create_response(failure_message), status=status.HTTP_400_BAD_REQUEST
            )

        return Response(
            create_response(success_message),
            status=status.HTTP_200_OK if create else status.HTTP_201_CREATED,
        )


@extend_schema_view(
    create=extend_schema(
        request={"multipart/form-data": SerieWriteSerializer}
    )
)
class SerieViewSet(
    GetSerializerClassMixin,
    GetPermissionsMixin,
    ActionMixin,
    ModelViewSet,
    HitCountMixin,
):
    queryset = Serie.objects.all()
    lookup_field = "pk"
    serializer_class = SerieSerializer
    filter_backends = [DjangoFilterBackend]
    filterset_class = SerieFilterSet
    serializer_action_classes = {
        "retrieve": SerieDetailSerializer,
        "create": SerieWriteSerializer,
        "update": SerieDetailSerializer,
        "partial_update": SerieDetailSerializer,
        "destroy": SerieDetailSerializer,
        "fav": GeneralActionResponse,
        "unfav": GeneralActionResponse,
        "follow": GeneralActionResponse,
        "unfollow": GeneralActionResponse,
        "donate": DonationActionSerializer,
        "rate": RatingActionSerializer,
        "chapters": ChapterSerializer,
        "releases": ChapterSerializer,
    }
    permission_classes = [NotBlackListed]
    permission_action_classes = {
        "create": [UploadSeriesPermission | IsAdminUser | ManageSeriesPermission],
        "update": [ManageSeriesPermission | IsAdminUser],
        "destroy": [ManageSeriesPermission | IsAdminUser],
        "fav": [IsAuthenticatedNotBlackListed],
        "unfav": [IsAuthenticatedNotBlackListed],
        "myfavs": [IsAuthenticatedNotBlackListed],
        "follow": [IsAuthenticatedNotBlackListed],
        "unfollow": [IsAuthenticatedNotBlackListed],
        "myfollows": [IsAuthenticatedNotBlackListed],
        "donate": [IsAuthenticatedNotBlackListed],
        "rate": [IsAuthenticatedNotBlackListed],
    }

    def get_queryset(self):
        qs = super().get_queryset()
        if self.action == "retrieve":
            qs = Serie.objects.for_details()

        if self.action in ["list", "myfavs", "myfollows"]:
            qs = Serie.objects.for_list().all()

        favorite_series_ids, followed_series_ids = (
            Favorite.objects.none(),
            Follow.objects.none(),
        )

        if self.action not in [
            "rate",
            "donate",
            "delete",
            "fav",
            "unfav",
            "update",
            "create",
            "follow",
            "unfollow",
        ]:

            if user := (
                self.request.user if self.request.user.is_authenticated else None
            ):
                favorite_series_ids = user.favorites.values_list("serie_id", flat=True)
                followed_series_ids = user.follows.values_list("serie_id", flat=True)

        self.favorite_series_ids = favorite_series_ids
        self.followed_series_ids = followed_series_ids

        return qs

    def get_serializer_context(self):

        favorite_series_ids = getattr(self, "favorite_series_ids", [])
        followed_series_ids = getattr(self, "followed_series_ids", [])
        favorite_series_ids = (
            set(favorite_series_ids)
            if not isinstance(favorite_series_ids, AlwaysTrueContainer)
            else favorite_series_ids
        )
        followed_series_ids = (
            set(followed_series_ids)
            if not isinstance(followed_series_ids, AlwaysTrueContainer)
            else followed_series_ids
        )
        context = super().get_serializer_context()
        context.update(
            {
                "favorite_series_ids": favorite_series_ids,
                "followed_series_ids": followed_series_ids,
            }
        )
        return context

    def retrieve(self, request, *args, **kwargs):
        serie = self.get_object()

        # Register a view hit {use it as read status}
        hit_count = HitCount.objects.get_for_object(serie)
        self.hit_count(request, hit_count)

        # Caching
        cache_key = make_cache_key_user_unique(request, f'serie_retrieve_{serie.pk}')
        cached_data = cache.get(cache_key)
        if cached_data is not None:
            return Response(cached_data)
        response = super().retrieve(request, *args, **kwargs)
        cache.set(cache_key, response.data, 60 * 15)  # 15 minutes

        return response

    def list(self, request, *args, **kwargs):
        cache_key = generate_cache_key(request, 'serie_list')
        cache_key = make_cache_key_user_unique(request, cache_key)
        cached_data = cache.get(cache_key)
        if cached_data is not None:
            return Response(cached_data)
        response = super().list(request, *args, **kwargs)
        cache.set(cache_key, response.data, 60 * 15)
        return response

    @extend_schema(
        description="Retrieve the latest updated series with their chapters.",
        responses={200: ReleaseSchemaSerializer(many=True)},
    )
    @action(methods=["GET"], detail=False, filterset_class=None)
    def releases(self, request):
        cache_key = generate_cache_key(request, 'serie_releases')
        cached_data = cache.get(cache_key)
        if cached_data is not None:
            return Response(cached_data)

        qs = Release.objects.all().order_by("-latest_chapter_updated_at")
        qs = self.paginate_queryset(qs)

        serializer = ReleaseSerializer(qs, many=True, context={"request": request})

        response: Response = self.get_paginated_response(serializer.data)
        cache.set(cache_key, response.data, 60 * 60)  # 1 hour 

        return response

    @extend_schema(
        responses={200: ChapterSerializer(many=True)},
    )
    @action(methods=["GET"], detail=True, filterset_class=None)
    def chapters(self, request, pk=None):
        # TODO: support ordering
        serie = self.get_object()

        cache_key = f'serie_{serie.pk}_chapters'
        cache_key = generate_cache_key(request, cache_key)
        cache_key = make_cache_key_user_unique(request, cache_key)

        cached_data = cache.get(cache_key)
        if cached_data is not None:
            return Response(cached_data)

        qs = serie.chapters.order_by("-order", "-created_at").all()
        if user := request.user if request.user.is_authenticated else None:
            read_status_subquery = ChapterRead.objects.filter(
                user=user, chapter=OuterRef("pk")
            )
            qs = qs.annotate(is_read=Exists(read_status_subquery))
        else:
            qs = qs.annotate(is_read=Value(False))

        chapters = self.paginate_queryset(qs)
        serializer = self.get_serializer(chapters, many=True)

        response: Response = self.get_paginated_response(serializer.data)
        cache.set(cache_key, response.data, 60 * 60)

        return response

    @extend_schema(responses={200: SerieSerializer(many=True)}, filters=False)
    @action(methods=["GET"], detail=False)
    def myfavs(self, request):
        cache_key = generate_cache_key(request, 'series_myfavs')
        cache_key = make_cache_key_user_unique(request, cache_key)
        cached_data = cache.get(cache_key)
        if cached_data is not None:
            return Response(cached_data)
        

        qs = (
            Favorite.objects.filter(user=request.user)
            .select_related("serie", "serie__type", "serie__status")
            .only(
                "serie__id",
                "serie__title",
                "serie__slug",
                "serie__type",
                "serie__status",
                "serie__poster",
                "serie__is_hot",
                "serie__views_count",
                "serie__rating",
            )
        )

        qs = self.paginate_queryset(qs)
        series = [favorite.serie for favorite in qs]
        self.favorite_series_ids = AlwaysTrueContainer()
        self.followed_series_ids = Follow.objects.filter(user=request.user).values_list(
            "serie", flat=True
        )
        serializer = self.get_serializer(series, many=True)

        response: Response = self.get_paginated_response(serializer.data)
        cache.set(cache_key, response.data, 60 * 15)

        return response

    @extend_schema(responses={200: SerieSerializer(many=True)}, filters=False)
    @action(methods=["GET"], detail=False)
    def myfollows(self, request):
        cache_key = generate_cache_key(request, 'series_myfollows')
        cache_key = make_cache_key_user_unique(request, cache_key)
        cached_data = cache.get(cache_key)
        if cached_data is not None:
            return Response(cached_data)
        
        qs = (
            Follow.objects.filter(user=request.user)
            .select_related("serie", "serie__type", "serie__status")
            .only(
                "serie__id",
                "serie__title",
                "serie__slug",
                "serie__type",
                "serie__status",
                "serie__poster",
                "serie__is_hot",
                "serie__views_count",
                "serie__rating",
            )
        )

        qs = self.paginate_queryset(qs)
        series = [f.serie for f in qs]
        self.followed_series_ids = AlwaysTrueContainer()
        self.favorite_series_ids = Favorite.objects.filter(
            user=request.user
        ).values_list("serie", flat=True)
        serializer = self.get_serializer(series, many=True)

        response: Response = self.get_paginated_response(serializer.data)
        cache.set(cache_key, response.data, 60 * 15)

        return response


    @extend_schema(
        request=DonationActionSerializer, responses={201: GeneralActionResponse}
    )
    @action(methods=["POST"], detail=True)
    def donate(self, request, pk=None):
        serie = self.get_object()
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer.save(serie=serie, user=request.user)
        data = create_response(f"Donated successfully to {serie.title}")
        return Response(data, status=status.HTTP_201_CREATED)

    @extend_schema(
        request=RatingActionSerializer, responses={201: GeneralActionResponse}
    )
    @action(methods=["POST"], detail=True)
    def rate(self, request, pk=None):
        serie = self.get_object()
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer.save(serie=serie, user=request.user)
        data = create_response(f"Rated successfully for {serie.title}")
        return Response(data, status=status.HTTP_201_CREATED)

    @extend_schema(
        request=None,
        responses={200: GeneralActionResponse, 400: None},
    )
    @action(methods=["POST"], detail=True)
    def fav(self, request, pk=None):
        serie = self.get_object()
        user = request.user
        _, created = Favorite.objects.get_or_create(serie=serie, user=user)
        if created:
            data = create_response(f"added to favorites")
            code = status.HTTP_200_OK
        else:
            data = create_response(f"you already have this serie in favorites")
            code = status.HTTP_400_BAD_REQUEST
        return Response(data, code)

    @extend_schema(
        request=None,
        responses={200: GeneralActionResponse, 400: None},
    )
    @action(methods=["POST"], detail=True)
    def unfav(self, request, pk=None):
        serie = self.get_object()
        user = request.user
        deleted = Favorite.objects.filter(serie=serie, user=user).delete()
        if deleted:
            data = create_response(f"removed serie form favorites")
            code = status.HTTP_200_OK
        else:
            data = create_response(f"you don't have this serie in favorites")
            code = status.HTTP_400_BAD_REQUEST
        return Response(data, code)

    @extend_schema(
        request=None,
        responses={200: GeneralActionResponse, 400: None},
    )
    @action(methods=["POST"], detail=True)
    def follow(self, request, pk=None):
        serie = self.get_object()
        user = request.user
        _, created = Follow.objects.get_or_create(serie=serie, user=user)
        if created:
            data = create_response(f"Now following serie {serie.title}")
            code = status.HTTP_200_OK
        else:
            data = create_response(f"already following serie {serie.title}")
            code = status.HTTP_400_BAD_REQUEST
        return Response(data, code)

    @extend_schema(
        request=None,
        responses={200: GeneralActionResponse, 400: None},
    )
    @action(methods=["POST"], detail=True)
    def unfollow(self, request, pk=None):
        serie = self.get_object()
        user = request.user
        deleted = Follow.objects.filter(serie=serie, user=user).delete()
        if deleted:
            data = create_response(f"unfollowed serie {serie.title}")
            code = status.HTTP_200_OK
        else:
            data = create_response(f"you're not following serie {serie.title}")
            code = status.HTTP_400_BAD_REQUEST
        return Response(data, code)


# TODO: implement caching for list
@extend_schema_view(
    create=extend_schema(
        request={"multipart/form-data": ChapterWriteSerializer}
    )
)
class ChapterViewSet(
    GetSerializerClassMixin, GetPermissionsMixin, ModelViewSet, HitCountMixin
):
    queryset = (
        Chapter.objects.select_related(
            "serie",
            "serie__type",
            "serie__status",
        )
        .order_by("updated_at")
        .all()
    )
    serializer_class = ChapterDetailSerializer
    lookup_field = "pk"
    filter_backends = [DjangoFilterBackend]
    filterset_class = ChapterFilterSet
    # pagination_class = ChapterCursorPagination
    serializer_action_classes = {
        "list": ChapterWithSerieSerializer,
        "create": ChapterWriteSerializer,
        "update": ChapterWriteSerializer,
        "partial_update": ChapterWriteSerializer,
    }

    permission_action_classes = {
        "create": [UploadChaptersPermission | IsAdminUser | ManageChaptersPermission],
        "update": [ManageChaptersPermission | IsAdminUser],
        "destroy": [ManageChaptersPermission | IsAdminUser],
        "history": [IsAuthenticatedNotBlackListed],
    }

    def get_favs_and_follows(self, user):
        favorite_series_ids, followed_series_ids = (
            Favorite.objects.none(),
            Follow.objects.none(),
        )

        if user and user.is_authenticated:
            favorite_series_ids = user.favorites.values_list("serie_id", flat=True)
            followed_series_ids = user.follows.values_list("serie_id", flat=True)

        return set(favorite_series_ids), set(followed_series_ids)

    def annotate_is_read(self, qs):
        if user := self.request.user if self.request.user.is_authenticated else None:
            read_status_subquery = ChapterRead.objects.filter(
                user=user, chapter=OuterRef("pk")
            )
            qs = qs.annotate(is_read=Exists(read_status_subquery))
        else:
            qs = qs.annotate(is_read=Value(False))
        return qs

    def get_queryset(self):
        qs = super().get_queryset()
        if self.action == "retrieve":
            qs = qs.prefetch_related("images")
        qs.defer(
            "serie__letter",
            "serie__cover",
            "serie__alternative",
            "serie__story",
            "serie__author_id",
            "serie__artist_id",
            "serie__published",
            "serie__allow_comments",
            "serie__ratings_count",
            "serie__followers_count",
            "serie__favorites_count",
            "serie__donations_count",
            "serie__chapters_count",
            "serie__created_at",
            "serie__created_by_id",
            "serie__updated_at",
            "serie__updated_by_id",
        )

        qs = self.annotate_is_read(qs)
        return qs

    def get_serializer_context(self):
        context = super().get_serializer_context()
        favs, follows = self.get_favs_and_follows(self.request.user)
        context.update(
            {
                "favorite_series_ids": favs,
                "followed_series_ids": follows,
            }
        )
        return context

    def retrieve(self, request, *args, **kwargs):
        chapter = self.get_object()

        # Register a view hit
        hit_count = HitCount.objects.get_for_object(chapter)
        self.hit_count(request, hit_count)

        # Register a read only if it hasn't been read yet
        if (
            request.user.is_authenticated
            and not ChapterRead.objects.filter(
                user=request.user, chapter=chapter
            ).exists()
        ):
            ChapterRead.objects.create(user=request.user, chapter=chapter)
        
        # Caching
        cache_key = make_cache_key_user_unique(request, f'chapters_retrieve_{chapter.pk}')
        cached_data = cache.get(cache_key)
        if cached_data is not None:
            return Response(cached_data)
        response = super().retrieve(request, *args, **kwargs)
        cache.set(cache_key, response.data, 60 * 15)  # 15 minutes

        return response


    @extend_schema(
        request=None, responses={200: GeneralActionResponse, 404: GeneralActionResponse}
    )
    # NOTE: unactivated
    # @action(methods=["POST"], detail=True)
    def mark_read(self, request, pk=None):
        chapter = self.get_object()
        return self.perform_action(
            request,
            {"chapter": chapter, "user": request.user},
            ChapterRead,
            "Chapter marked as read",
            "Chapter already has been read",
        )

    @extend_schema(
        request=None, responses={200: GeneralActionResponse, 404: GeneralActionResponse}
    )
    # NOTE: unactivated
    # @action(methods=["POST"], detail=True)
    def mark_unread(self, request, pk=None):
        chapter = self.get_object()
        return self.perform_action(
            request,
            {"chapter": chapter, "user": request.user},
            ChapterRead,
            "Chapter marked as unread",
            "Chapter is not marked as read",
            create=False,
        )

    @extend_schema(
        responses={200: ChapterImageSerializer(many=True)},
        filters=False,
        parameters=[],
    )
    @action(methods=["GET"], detail=True, pagination_class=None)
    def images(self, request, pk=None):
        chapter = self.get_object()

        # Caching
        cache_key = make_cache_key_user_unique(request, f'chapter_images_{chapter.pk}')
        cached_data = cache.get(cache_key)
        if cached_data is not None:
            return Response(cached_data)

        images = chapter.images.all()
        serializer = ChapterImageSerializer(
            images, many=True, context={"request": request}
        )

        response = Response(serializer.data)
        cache.set(cache_key, response.data, 60 * 15)  # 15 minutes
        return response

    @extend_schema(
        responses={200: ChapterWithSerieSerializer(many=True)},
        filters=False,
        parameters=[
            OpenApiParameter(
                "order_by", enum=["read_time", "-read_time"], default="-read_time"
            )
        ],
    )
    @action(methods=["GET"], detail=False)
    def history(self, request, pk=None):
        ordering = (
            "created_at"
            if request.query_params.get("order_by") == "read_time"
            else "-created_at"
        )
        # Caching
        cache_key = generate_cache_key(request, f'chapters_history')
        cache_key = make_cache_key_user_unique(request, cache_key)
        cache_key += f"_{ordering}"

        cached_data = cache.get(cache_key)
        if cached_data is not None:
            return Response(cached_data)

        queryset = (
            ChapterRead.objects.filter(user=request.user)
            .order_by(ordering)
            .defer("user", "chapter")
        )
        queryset = self.paginate_queryset(queryset)
        ids = [cr.chapter_id for cr in queryset]
        ordering = Case(*[When(id=pk, then=pos) for pos, pk in enumerate(ids)])
        chapters = self.get_queryset().filter(id__in=ids).order_by(ordering)
        serializer = ChapterWithSerieSerializer(chapters, many=True)

        response = self.get_paginated_response(serializer.data)
        cache.set(cache_key, response.data, 60 * 15)  # 15 minutes

        return response


@method_decorator(cache_page(60 * 60 * 24), "list")  # 24 hours
@method_decorator(cache_page(60 * 60 * 24), "retrieve")
class SerieTypeViewSet(ReadOnlyModelViewSet):
    queryset = SerieType.objects.all()
    lookup_field = "pk"
    serializer_class = SerieTypeSerializer
    permission_classes = [NotBlackListed]
    pagination_class = None

@method_decorator(cache_page(60 * 60 * 24), "list")  # 24 hours
@method_decorator(cache_page(60 * 60 * 24), "retrieve")
class SerieStatusViewSet(ReadOnlyModelViewSet):
    queryset = SerieStatus.objects.all()
    lookup_field = "pk"
    serializer_class = SerieStatusSerializer
    permission_classes = [NotBlackListed]
    pagination_class = None

# TODO: consider caching
class SerieAuthorViewSet(ReadOnlyModelViewSet):
    queryset = SerieAuthor.objects.all()
    lookup_field = "pk"
    serializer_class = SerieAuthorSerializer
    permission_classes = [NotBlackListed]
    filter_backends = [DjangoFilterBackend]
    filterset_class = SerieAuthorFilterSet

# TODO: consider caching
class SerieArtistViewSet(ReadOnlyModelViewSet):
    queryset = SerieArtist.objects.all()
    lookup_field = "pk"
    serializer_class = SerieArtistSerializer
    permission_classes = [NotBlackListed]
    filter_backends = [DjangoFilterBackend]
    filterset_class = SerieArtistFilterSet

# TODO: consider caching
class GenreViewSet(ReadOnlyModelViewSet):
    queryset = Genre.objects.all()
    lookup_field = "pk"
    serializer_class = GenreSerializer
    permission_classes = [NotBlackListed]
    pagination_class = None
    filter_backends = [DjangoFilterBackend]
    filterset_class = GenreFilterSet