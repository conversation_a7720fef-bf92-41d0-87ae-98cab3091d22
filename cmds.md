# IpTables for ssh tunnels

## Allow localhost replace port with dest port
```
sudo iptables -A INPUT -p tcp -s 127.0.0.1 --dport 8404 -j ACCEPT
```
## Block all others
```
sudo iptables -A INPUT -p tcp --dport 8404 -j DROP
```
## Save rules (persistent after reboot)
```
sudo iptables-save | sudo tee /etc/iptables/rules.v4
```

## New ip exposing with cpanel
- open config 
```
vim /etc/csf/csf.conf
```
- Edit the following line to add a new port
```
# Allow incoming TCP ports
TCP_IN = "20,21,22,25,53,80,110,143,443,465,587,853,993,995,2077,2078,2079,2080,2082,2083,2086,2087,2095,2096,8443,8000,8010"
```
- save changes
```
sudo csf -r
```

# Check haproxy stats
```
ssh -L 8404:localhost:8404 qubit@**************
```
then access `http://localhost:8404/stats` in the browser.

** access prometheus
```
ssh -L 9090:localhost:9090 youruser@your-server-ip
```

** access both
```
ssh -L 8404:localhost:8404 -L 9090:localhost:9090 youruser@your-server-ip
```
