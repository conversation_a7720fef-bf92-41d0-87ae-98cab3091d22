import logging
from urllib.parse import urljoin
from django.dispatch import receiver
from django_comments_xtd.signals import should_request_be_authorized
from django_comments_xtd.models import XtdComment, LIKEDIT_FLAG
from django.contrib.sites.models import Site

from django_comments.models import CommentFlag
from django.db.models.signals import post_save, post_delete
from django.core.cache import cache
from fcm_django.models import FCMDevice
from swatmanga.common.utils import (
    format_using_attributes,
    get_current_site_url,
    get_formated_fcm_notification,
)
from .notifications import MESSAGES, TOPICS

from firebase_admin.messaging import AndroidConfig, AndroidNotification
from django.utils.text import Truncator


TRUNCATED_COMMENT_LENGTH = 100


@receiver(should_request_be_authorized)
def check_authorized(sender, comment, request, **kwargs):
    if request.user and request.user.is_authenticated:
        return True


@receiver([post_save, post_delete], sender=XtdComment)
def invalidate_comments_cache(sender, instance: XtdComment, **kwargs):
    key = f"comments_{instance.content_type.app_label}.{instance.content_type.model}_{instance.object_pk}*"
    keys = cache.keys(key)
    if keys:
        cache.delete_many(keys)


@receiver(post_save, sender=XtdComment)
def notify_new_reply(sender, instance: XtdComment, created: bool, **kwargs):
    if not created or instance.parent_id == instance.id:
        return

    org_cmt = XtdComment.objects.filter(id=instance.parent_id).first()
    if not org_cmt or instance.user == org_cmt.user:
        return

    # Fetch all replies to the original comment, excluding the original itself
    replies = XtdComment.objects.filter(parent_id=org_cmt.id).exclude(id=org_cmt.id).order_by('-submit_date')
    reply_count = replies.count()
    latest_replies = replies[:4]  # Get the two latest replies

    org_cmt_str = Truncator(org_cmt.comment).chars(TRUNCATED_COMMENT_LENGTH)

    # Build the title with the reply count (handle pluralization as needed)
    title = f"💬 {reply_count} رد على تعليقك"

    # Build the body with original comment and latest replies
    body_lines = [
        f"{instance.content_object}",
        f"أنت: {org_cmt_str}"
        ]
    for reply in latest_replies:
        user_display = f"من {reply.user.display_name}"
        truncated_comment = Truncator(reply.comment).chars(TRUNCATED_COMMENT_LENGTH)
        body_lines.append(f"{user_display}: {truncated_comment}")

    body = "\n".join(body_lines)

    # Build image url
    image = None
    content_type_model = instance.content_type.model.lower()
    if content_type_model == "serie":
        image = instance.content_object.thumbnail
    elif content_type_model == "chapter":
        image = instance.content_object.serie.thumbnail
    
    image = image.url if image else None

    s = get_current_site_url()
    image = urljoin(s, image.lstrip("/")) if image else None

    # Android configuration with collapse key to group notifications
    key = f"comment_{org_cmt.id}_replies"
    notification_config = AndroidNotification(tag=key, notification_count=reply_count, icon=image)
    android_config = AndroidConfig(
        collapse_key=f"comment_{org_cmt.id}_replies", notification=notification_config
    )

    # Create the FCM message
    message = get_formated_fcm_notification(
        instance,
        title=title,
        body=body,
        android=android_config,
    )

    # Send to the user's devices
    devices = FCMDevice.objects.filter(user=org_cmt.user)
    devices.send_message(message)


@receiver(post_save, sender=CommentFlag)
def notify_new_like(sender, instance: CommentFlag, created: bool, **kwargs):
    if not created or instance.user == instance.comment.user:
        return

    org_cmt = instance.comment
    cmt_str = Truncator(org_cmt.comment).chars(TRUNCATED_COMMENT_LENGTH)

    total = CommentFlag.objects.filter(comment=org_cmt, flag=LIKEDIT_FLAG).count()

    key = f"comment_{org_cmt.id}_replies"

    title = f"حاز تعليقك على إعجاب {instance.user.display_name}"
    if total > 1:
        title += f" و {total} آخرون"
    body_lines = [
        f"{org_cmt.content_object}",
        f"أنت: {cmt_str}"
        ]
    body = "\n".join(body_lines)
    message = {"title": title, "body": body}

    # Build image url
    image = None
    content_type_model = org_cmt.content_type.model.lower()
    if content_type_model == "serie":
        image = org_cmt.content_object.thumbnail
    elif content_type_model == "chapter":
        image = org_cmt.content_object.serie.thumbnail

    image = image.url if image else None

    s = get_current_site_url()
    image = urljoin(s, image.lstrip("/")) if image else None

    android_notification_config = AndroidNotification(tag=key, notification_count=total, icon=image)
    android_config = AndroidConfig(
        collapse_key=key, notification=android_notification_config
    )

    message = get_formated_fcm_notification(
        instance,
        title=message["title"],
        body=message["body"],
        android=android_config,
    )
    devices = FCMDevice.objects.filter(user=org_cmt.user)
    devices.send_message(message)
