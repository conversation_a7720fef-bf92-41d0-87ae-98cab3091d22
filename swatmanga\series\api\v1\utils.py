from babel.dates import format_timedelta
from datetime import datetime
from django.utils import timezone


def humanize_time_difference(past_time, current_time=None, locale="en"):
    if current_time is None:
        current_time = timezone.now()

    # Calculate the time difference
    diff = current_time - past_time

    # Handle future and past differences
    if diff.total_seconds() < 0:
        diff = -diff

    return f"{format_timedelta(diff, locale=locale)}"
