# Generated by Django 5.0.8 on 2024-08-13 09:11

import computedfields.resolver
import django.db.models.deletion
import versatileimagefield.fields
from django.conf import settings
from django.db import migrations, models

import swatmanga.series.models


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="Genre",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=50, unique=True)),
            ],
            options={
                "db_table": "genres",
            },
        ),
        migrations.CreateModel(
            name="SerieStatus",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.Char<PERSON>ield(max_length=30, unique=True)),
            ],
            options={
                "db_table": "serie_statuses",
            },
        ),
        migrations.CreateModel(
            name="SerieType",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=30, unique=True)),
            ],
            options={
                "db_table": "serie_types",
            },
        ),
        migrations.CreateModel(
            name="Chapter",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("title", models.CharField(max_length=255)),
                ("slug", models.CharField(max_length=255)),
                ("number", models.CharField(blank=True, max_length=100, null=True)),
                ("published", models.DateTimeField(blank=True, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("views_count", models.IntegerField(editable=False)),
                ("images_count", models.IntegerField(editable=False)),
                (
                    "created_by",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="created_chapters",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "db_table": "chapters",
                "ordering": ["serie", "number"],
            },
            bases=(computedfields.resolver._ComputedFieldsModelBase, models.Model),
        ),
        migrations.CreateModel(
            name="ChapterImage",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "image",
                    swatmanga.series.models.WebPVersatileImageField(
                        blank=True,
                        null=True,
                        upload_to=swatmanga.series.models.chapter_image_upload_to,
                        verbose_name="Chapter Image",
                    ),
                ),
                (
                    "image_ppoi",
                    versatileimagefield.fields.PPOIField(
                        default="0.5x0.5",
                        editable=False,
                        max_length=20,
                        verbose_name="Chapter Image PPOI",
                    ),
                ),
                ("order", models.PositiveIntegerField()),
                (
                    "chapter",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="images",
                        to="series.chapter",
                    ),
                ),
            ],
            options={
                "db_table": "chapter_images",
                "ordering": ["chapter", "order"],
            },
        ),
        migrations.CreateModel(
            name="ChapterView",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "chapter",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="chapterviews",
                        to="series.chapter",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "db_table": "chapter_views",
            },
        ),
        migrations.CreateModel(
            name="Serie",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("title", models.CharField(max_length=255)),
                ("slug", models.SlugField(max_length=255, unique=True)),
                ("letter", models.CharField(blank=True, max_length=1, null=True)),
                (
                    "cover",
                    swatmanga.series.models.WebPVersatileImageField(
                        blank=True,
                        null=True,
                        upload_to=swatmanga.series.models.serie_cover_upload_to,
                        verbose_name="Cover Image",
                    ),
                ),
                (
                    "cover_ppoi",
                    versatileimagefield.fields.PPOIField(
                        default="0.5x0.5",
                        editable=False,
                        max_length=20,
                        verbose_name="Cover Image PPOI",
                    ),
                ),
                (
                    "alternative",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                ("story", models.TextField(max_length=5000)),
                ("author", models.CharField(blank=True, max_length=100, null=True)),
                ("artist", models.CharField(blank=True, max_length=100, null=True)),
                ("published", models.DateTimeField(blank=True, null=True)),
                ("views_count", models.IntegerField(editable=False)),
                ("rating", models.IntegerField(editable=False)),
                ("ratings_count", models.IntegerField(editable=False)),
                ("followers_count", models.IntegerField(editable=False)),
                ("favorites_count", models.IntegerField(editable=False)),
                ("donations_count", models.FloatField(editable=False)),
                ("chapters_count", models.IntegerField(editable=False)),
                ("is_hot", models.BooleanField(db_index=True, default=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "created_by",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="created_series",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "genres",
                    models.ManyToManyField(
                        blank=True, related_name="series", to="series.genre"
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "status",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="series",
                        to="series.seriestatus",
                    ),
                ),
                (
                    "type",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="series",
                        to="series.serietype",
                    ),
                ),
            ],
            options={
                "db_table": "series",
            },
            bases=(computedfields.resolver._ComputedFieldsModelBase, models.Model),
        ),
        migrations.CreateModel(
            name="Rating",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("rating", models.PositiveSmallIntegerField()),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "serie",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="ratings",
                        to="series.serie",
                    ),
                ),
            ],
            options={
                "db_table": "ratings",
            },
        ),
        migrations.CreateModel(
            name="Follow",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "serie",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="follows",
                        to="series.serie",
                    ),
                ),
            ],
            options={
                "db_table": "follows",
            },
        ),
        migrations.CreateModel(
            name="Donation",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("amount", models.DecimalField(decimal_places=2, max_digits=10)),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "serie",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="donations",
                        to="series.serie",
                    ),
                ),
            ],
            options={
                "db_table": "donations",
            },
        ),
        migrations.AddField(
            model_name="chapter",
            name="serie",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="chapters",
                to="series.serie",
            ),
        ),
        migrations.CreateModel(
            name="SerieView",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "serie",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="serieviews",
                        to="series.serie",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "db_table": "serie_views",
            },
        ),
    ]
