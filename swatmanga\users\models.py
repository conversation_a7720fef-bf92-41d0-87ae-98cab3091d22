import os

from django.conf import settings
from django.contrib.auth.models import AbstractUser
from django.core.validators import FileExtensionValidator
from django.db.models import <PERSON><PERSON><PERSON><PERSON>, DateTimeField, EmailField, PositiveIntegerField
from django.urls import reverse
from django.utils.translation import gettext_lazy as _
from typing import ClassVar

from swatmanga.common.fields import WebPVersatileImage<PERSON>ield
from versatileimagefield.fields import PPOIField

from .managers import User<PERSON>anager


def user_avatar_upload_to(instance, filename):
    # Builds the path: 'users/user_id/avatar.webp'
    parent_dir = str(instance.id)

    # for mock data
    if parent_dir is None:
        parent_dir = instance.username
    return os.path.join("users", parent_dir, "avatar.webp")


class User(AbstractUser):
    """
    Default custom user model for SwatMangaReborn.
    If adding fields that need to be filled at user signup,
    check forms.SignupForm and forms.SocialSignupForms accordingly.
    """

    username = Char<PERSON><PERSON>(
        _("username"),
        max_length=255,
        unique=True,
        help_text=_(
            "Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only."
        ),
        validators=[AbstractUser.username_validator],
        error_messages={
            "unique": _("A user with that username already exists."),
        },
    )
    # First and last name do not cover name patterns around the globe
    name = CharField(_("Name of User"), blank=True, max_length=255)
    first_name = None  # type: ignore[assignment]
    last_name = None  # type: ignore[assignment]
    email = EmailField(_("email address"), unique=True)
    avatar = WebPVersatileImageField(
        "Avatar Image",
        upload_to=user_avatar_upload_to,
        blank=True,
        null=True,
        lossy_quality=93,
        validators=[
            FileExtensionValidator(allowed_extensions=settings.ALLOWED_IMAGE_EXTENSIONS)
        ],
    )
    avatar_ppoi = PPOIField()
    coins = PositiveIntegerField(default=0)
    date_joined = None  # type: ignore[assignment]
    # Records
    created_at = DateTimeField(auto_now_add=True, null=True)
    updated_at = DateTimeField(auto_now=True)

    REQUIRED_FIELDS = ["name", "email"]

    objects: ClassVar[UserManager] = UserManager()

    @property
    def display_name(self):
        return self.name if self.name else self.get_username()

    def get_absolute_url(self) -> str:
        """Get URL for user's detail view.

        Returns:
            str: URL for user detail.

        """
        return reverse("users:profile", kwargs={"pk": self.id})

    def cropped_avatar(self, dim='200x200'):
        if not self.avatar:
            return None
        return self.avatar.crop[dim]

    @property
    def vip(self) -> bool:
        if self.plans_subscriptions:
            status = self.plans_subscriptions.model.PlanSubscriptionStatus.SUBSCRIBED
            return self.plans_subscriptions.filter(status=status).exists()
        return False

    @property
    def ads_enabled(self) -> bool:
        if self.plans_subscriptions:
            status = self.plans_subscriptions.model.PlanSubscriptionStatus.SUBSCRIBED
            return not self.plans_subscriptions.filter(status=status, plan__ads_enabled=True).exists()
        return True
