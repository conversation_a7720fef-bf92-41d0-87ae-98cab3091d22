from django.contrib.auth import get_user_model


User = get_user_model()


def get_user_groups(user: User):
    return user.groups.all()


def user_has_any_group(user: User, group_names: list[str]) -> bool:
    if not group_names:
        return False

    return user.groups.filter(name__in=group_names).exists()


def user_has_groups(user: User, group_names: list[str]) -> bool:
    if not group_names:
        return False

    return all(
        group_name in user.groups.values_list("name", flat=True)
        for group_name in group_names
    )


def user_has_group(user: User, group_name: str) -> bool:
    return user.groups.filter(name=group_name).exists()
