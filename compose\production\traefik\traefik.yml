log:
  level: INFO

entryPoints:
  web:
    # http
    address: ':80'
    http:
      # https://doc.traefik.io/traefik/routing/entrypoints/#entrypoint
      redirections:
        entryPoint:
          to: web-secure

  web-secure:
    # https
    address: ':443'

certificatesResolvers:
  letsencrypt:
    # https://doc.traefik.io/traefik/https/acme/#lets-encrypt
    acme:
      email: '<EMAIL>'
      storage: /etc/traefik/acme/acme.json
      # https://doc.traefik.io/traefik/https/acme/#httpchallenge
      httpChallenge:
        entryPoint: web

http:
  routers:
    web-secure-router:
      rule: 'Host(`example.com`) || Host(`www.example.com`)'
      entryPoints:
        - web-secure
      middlewares:
        - csrf
      service: django
      tls:
        # https://doc.traefik.io/traefik/routing/routers/#certresolver
        certResolver: letsencrypt

    web-media-router:
      rule: '(Host(`example.com`) || Host(`www.example.com`)) && PathPrefix(`/media/`)'
      entryPoints:
        - web-secure
      middlewares:
        - csrf
      service: django-media
      tls:
        certResolver: letsencrypt

  middlewares:
    csrf:
      # https://doc.traefik.io/traefik/master/middlewares/http/headers/#hostsproxyheaders
      # https://docs.djangoproject.com/en/dev/ref/csrf/#ajax
      headers:
        hostsProxyHeaders: ['X-CSRFToken']

  services:
    django:
      loadBalancer:
        servers:
          - url: http://django:5000

    django-media:
      loadBalancer:
        servers:
          - url: http://nginx:80

providers:
  # https://doc.traefik.io/traefik/master/providers/file/
  file:
    filename: /etc/traefik/traefik.yml
    watch: true
