<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="pytest: ." type="tests" factoryName="py.test" singleton="true">
    <module name="swatmanga" />
    <option name="INTERPRETER_OPTIONS" value="" />
    <option name="PARENT_ENVS" value="true" />
    <option name="SDK_HOME" value="" />
    <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
    <option name="IS_MODULE_SDK" value="true" />
    <option name="ADD_CONTENT_ROOTS" value="true" />
    <option name="ADD_SOURCE_ROOTS" value="true" />
    <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
    <PathMappingSettings>
      <option name="pathMappings">
        <list>
          <mapping local-root="$PROJECT_DIR$" remote-root="/app" />
        </list>
      </option>
    </PathMappingSettings>
    <option name="_new_keywords" value="&quot;&quot;" />
    <option name="_new_additionalArguments" value="&quot;&quot;" />
    <option name="_new_target" value="&quot;.&quot;" />
    <option name="_new_targetType" value="&quot;PATH&quot;" />
    <method />
  </configuration>
</component>
