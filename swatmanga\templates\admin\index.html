{% extends "admin/base_site.html" %}
{% load i18n static jazzmin %}
{% get_jazzmin_ui_tweaks as jazzmin_ui %}

{% block extrahead %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-datalabels@2.0.0"></script>
<style>
    /* Reduce the height for DB charts */
    #dbConnectionsChart,
    #dbQueryTypesChart,
    #dbQueryDurationChart {
        height: 150px !important;
    }

    /* Additional styles for mobile devices */
    @media (max-width: 576px) {
        .small-box h3 {
            font-size: 1.2rem;
        }

        .small-box p {
            font-size: 0.8rem;
        }

        .card-header h3 {
            font-size: 1rem;
        }

        /* Ensure canvases scale properly */
        canvas {
            width: 100% !important;
            height: auto !important;
        }
    }
</style>
{% endblock %}

{% block bodyclass %}{{ block.super }} dashboard{% endblock %}

{% block content_title %} {% trans 'System Dashboard' %} {% endblock %}

{% block breadcrumbs %}
<ol class="breadcrumb">
    <li class="breadcrumb-item"><a href="{% url 'admin:index' %}">{% trans 'Home' %}</a></li>
    <li class="breadcrumb-item">{% trans 'Dashboard' %}</li>
</ol>
{% endblock %}

{% block content %}
<div id="metrics-data" data-metrics-url="{% url 'metrics' %}"></div>
<div class="container-fluid">
    <div class="row">
        <!-- Main Content -->
        <div class="col-md col-12">
            <!-- System Health Cards -->
            <div class="row">
                <div class="col-lg-3 col-6">
                    <div class="small-box bg-info">
                        <div class="inner">
                            <h3 id="memory-usage">0</h3>
                            <p>Memory Usage (MB)</p>
                        </div>
                        <div class="icon">
                            <i class="fas fa-memory"></i>
                        </div>
                    </div>
                </div>

                <div class="col-lg-3 col-6">
                    <div class="small-box bg-success">
                        <div class="inner">
                            <h3 id="cpu-time">0</h3>
                            <p>CPU Time (seconds)</p>
                        </div>
                        <div class="icon">
                            <i class="fas fa-microchip"></i>
                        </div>
                    </div>
                </div>

                <div class="col-lg-3 col-6">
                    <div class="small-box bg-warning">
                        <div class="inner">
                            <h3 id="db-connections">0</h3>
                            <p>DB Connections</p>
                        </div>
                        <div class="icon">
                            <i class="fas fa-database"></i>
                        </div>
                    </div>
                </div>

                <div class="col-lg-3 col-6">
                    <div class="small-box bg-danger">
                        <div class="inner">
                            <h3 id="total-requests">0</h3>
                            <p>HTTP Requests</p>
                        </div>
                        <div class="icon">
                            <i class="fas fa-network-wired"></i>
                        </div>
                    </div>
                </div>
            </div>

            <!-- HTTP Metrics Section -->
            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Request Methods</h3>
                        </div>
                        <div class="card-body">
                            <canvas id="httpMethodsChart" style="height: 250px;"></canvas>
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Response Statuses</h3>
                        </div>
                        <div class="card-body">
                            <canvas id="httpStatusChart" style="height: 250px;"></canvas>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <!-- Requests by View -->
                    <div class="col-md-6 col-12">
                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">Requests by View</h3>
                            </div>
                            <div class="card-body">
                                <canvas id="requestsByViewChart" style="height: 300px;"></canvas>
                            </div>
                        </div>
                    </div>

                    <!-- Slowest View -->
                    <div class="col-md-6 col-12">
                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">Slowest Views by Response Time</h3>
                            </div>
                            <div class="card-body">
                                <canvas id="slowViewsChart" style="height: 300px;"></canvas>
                            </div>
                        </div>
                    </div>
                </div>


            </div>
        </div>
    </div>

    <!-- Latency Metrics -->
    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Request Latency</h3>
                </div>
                <div class="card-body">
                    <canvas id="latencyChart" style="height: 250px;"></canvas>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Payload Sizes</h3>
                </div>
                <div class="card-body">
                    <canvas id="payloadChart" style="height: 250px;"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Database Section -->
    <div class="row">
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Database Connections</h3>
                </div>
                <div class="card-body">
                    <!-- Adjusted height via CSS -->
                    <canvas id="dbConnectionsChart"></canvas>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Query Types</h3>
                </div>
                <div class="card-body">
                    <!-- Adjusted height via CSS -->
                    <canvas id="dbQueryTypesChart"></canvas>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Query Duration</h3>
                </div>
                <div class="card-body">
                    <canvas id="dbQueryDurationChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Cache Metrics Section -->
    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Cache Hits & Misses</h3>
                </div>
                <div class="card-body">
                    <canvas id="cacheHitsMissesChart" style="height: 250px;"></canvas>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Cache Operations</h3>
                </div>
                <div class="card-body">
                    <canvas id="cacheOperationsChart" style="height: 250px;"></canvas>
                </div>
            </div>
        </div>
    </div>

</div>
</div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function () {
        const chartColors = {
            info: '#17a2b8',
            success: '#28a745',
            warning: '#ffc107',
            danger: '#dc3545',
            primary: '#007bff',
            secondary: '#6c757d'
        };

        // Initialize charts with maintainAspectRatio disabled so the CSS height is used
        const charts = {
            dbConnections: new Chart(document.getElementById('dbConnectionsChart'), {
                type: 'doughnut',
                data: {
                    labels: ['Active', 'Max'],
                    datasets: [{
                        data: [0, 0],
                        backgroundColor: [chartColors.info, chartColors.secondary]
                    }]
                },
                options: {
                    maintainAspectRatio: false
                }
            }),

            dbQueryTypes: new Chart(document.getElementById('dbQueryTypesChart'), {
                type: 'pie',
                data: {
                    labels: ['SELECT', 'INSERT', 'UPDATE', 'DELETE'],
                    datasets: [{
                        data: [0, 0, 0, 0],
                        backgroundColor: [
                            chartColors.primary,
                            chartColors.success,
                            chartColors.warning,
                            chartColors.danger
                        ]
                    }]
                },
                options: {
                    maintainAspectRatio: false
                }
            }),

            dbQueryDuration: new Chart(document.getElementById('dbQueryDurationChart'), {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: 'Query Duration (ms)',
                        data: [],
                        borderColor: chartColors.danger,
                        tension: 0.1
                    }]
                },
                options: {
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            title: {
                                display: true,
                                text: 'Execution Count'
                            }
                        },
                        x: {
                            title: {
                                display: true,
                                text: 'Duration Buckets'
                            }
                        }
                    }
                }
            }),

            httpMethods: new Chart(document.getElementById('httpMethodsChart'), {
                type: 'bar',
                data: {
                    labels: ['GET', 'POST', 'PUT', 'DELETE'],
                    datasets: [{
                        label: 'Requests',
                        data: [0, 0, 0, 0],
                        backgroundColor: [
                            chartColors.primary,
                            chartColors.success,
                            chartColors.warning,
                            chartColors.danger
                        ]
                    }]
                }
            }),

            httpStatus: new Chart(document.getElementById('httpStatusChart'), {
                type: 'bar',
                data: {
                    labels: ['2xx', '3xx', '4xx', '5xx'],
                    datasets: [{
                        data: [0, 0, 0, 0],
                        backgroundColor: [
                            chartColors.success,
                            chartColors.info,
                            chartColors.warning,
                            chartColors.danger
                        ]
                    }]
                }
            }),

            requestsByViewChart: new Chart(document.getElementById('requestsByViewChart'), {
                type: 'bar',
                data: {
                    labels: [],
                    datasets: [{
                        label: 'Request Count',
                        data: [],
                        backgroundColor: chartColors.primary
                    }]
                },
                options: {
                    indexAxis: 'y',  // This makes the bars horizontal
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        x: {
                            beginAtZero: true,
                            title: { display: true, text: 'Request Count' }
                        },
                        y: {
                            title: { display: true, text: 'View' }
                        }
                    }
                }
            }),

            slowViewsChart: new Chart(document.getElementById('slowViewsChart'), {
                type: 'bar',
                data: {
                    labels: [],
                    datasets: [{
                        label: 'Avg Response Time (ms)',
                        data: [],
                        backgroundColor: chartColors.danger
                    }]
                },
                options: {
                    indexAxis: 'y',  // Horizontal bars
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        x: {
                            beginAtZero: true,
                            title: { display: true, text: 'Milliseconds' }
                        },
                        y: {
                            title: { display: true, text: 'View' }
                        }
                    }
                }
            }),

            latencyChart: new Chart(document.getElementById('latencyChart'), {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: 'Request Latency (ms)',
                        data: [],
                        borderColor: chartColors.primary,
                        tension: 0.1
                    }]
                }
            }),

            payloadChart: new Chart(document.getElementById('payloadChart'), {
                type: 'bar',
                data: {
                    labels: ['Requests', 'Responses'],
                    datasets: [{
                        label: 'Payload Size (KB)',
                        data: [0, 0],
                        backgroundColor: [
                            chartColors.primary,
                            chartColors.info
                        ]
                    }]
                }
            }),

            cacheHitsMisses: new Chart(document.getElementById('cacheHitsMissesChart'), {
                type: 'bar',
                data: {
                    labels: ['Hits', 'Misses'],
                    datasets: [{
                        label: 'Cache Hits & Misses',
                        data: [0, 0],
                        backgroundColor: [
                            chartColors.success,
                            chartColors.danger
                        ]
                    }]
                }
            }),

            cacheOperations: new Chart(document.getElementById('cacheOperationsChart'), {
                type: 'bar',
                data: {
                    labels: ['Gets', 'Sets', 'Deletes'],
                    datasets: [{
                        label: 'Cache Operations',
                        data: [0, 0, 0],
                        backgroundColor: [
                            chartColors.primary,
                            chartColors.success,
                            chartColors.danger
                        ]
                    }]
                }
            }),
        };

        const metricsUrl = document.getElementById('metrics-data').dataset.metricsUrl;
        // Metric processing functions
        async function fetchMetrics() {
            try {
                const response = await fetch(metricsUrl);
                const data = await response.text();
                processMetrics(data);
            } catch (error) {
                console.error('Error fetching metrics:', error);
            }
        }

        function processMetrics(metricsText) {
            const metrics = {};
            const metricRegex = /^([a-zA-Z_:][a-zA-Z0-9_:]*)(?:\{(.*)\})?\s+([0-9.Ee+-]+)$/;
            metricsText.split('\n').forEach(line => {
                if (line.startsWith('#') || !line.trim()) return;
                const match = line.match(metricRegex);
                if (!match) return;
                const name = match[1];
                const labelPart = match[2] || '';
                const value = parseFloat(match[3]);
                const labels = labelPart.split(',').reduce((acc, pair) => {
                    const [key, val] = pair.split('=');
                    if (key && val) acc[key] = val.replace(/"/g, '');
                    return acc;
                }, {});
                if (!metrics[name]) metrics[name] = [];
                metrics[name].push({ labels, value });
            });
            updateDashboard(metrics);
        }

        function updateDashboard(metrics) {
            document.getElementById('memory-usage').textContent =
                ((metrics.process_virtual_memory_bytes?.[0]?.value || 0) / 1024 / 1024).toFixed(2);
            document.getElementById('cpu-time').textContent =
                (metrics.process_cpu_seconds_total?.[0]?.value || 0).toFixed(2);
            document.getElementById('db-connections').textContent =
                metrics.django_db_new_connections_total?.find(m => m.labels.vendor === 'postgresql')?.value || 0;
            document.getElementById('total-requests').textContent =
                metrics.django_http_requests_total_by_method_total?.find(m => m.labels.method === 'GET')?.value || 0;

            charts.dbConnections.data.datasets[0].data = [
                metrics.django_db_new_connections_total?.[0]?.value || 0,
                metrics.process_max_fds?.[0]?.value || 0
            ];
            charts.dbConnections.update();

            charts.dbQueryTypes.data.datasets[0].data = [
                metrics.django_db_execute_total?.find(m => m.labels.vendor === 'postgresql')?.value || 0,
                metrics.django_model_inserts_total?.[0]?.value || 0,
                metrics.django_model_updates_total?.[0]?.value || 0,
                metrics.django_model_deletes_total?.[0]?.value || 0
            ];
            charts.dbQueryTypes.update();

            const queryDurationBuckets = metrics.django_db_query_duration_seconds_bucket || [];
            if (queryDurationBuckets.length > 0) {
                charts.dbQueryDuration.data.labels = queryDurationBuckets
                    .filter(b => parseFloat(b.labels.le) < Infinity)
                    .map(b => `${(parseFloat(b.labels.le) * 1000).toFixed(2)}ms`);

                charts.dbQueryDuration.data.datasets[0].data = queryDurationBuckets
                    .filter(b => parseFloat(b.labels.le) < Infinity)
                    .map(b => b.value);
                charts.dbQueryDuration.update();
            }

            charts.httpMethods.data.datasets[0].data = [
                metrics.django_http_requests_total_by_method_total?.find(m => m.labels.method === 'GET')?.value || 0,
                metrics.django_http_requests_total_by_method_total?.find(m => m.labels.method === 'POST')?.value || 0,
                metrics.django_http_requests_total_by_method_total?.find(m => m.labels.method === 'PUT')?.value || 0,
                metrics.django_http_requests_total_by_method_total?.find(m => m.labels.method === 'DELETE')?.value || 0
            ];
            charts.httpMethods.update();

            charts.httpStatus.data.datasets[0].data = [
                metrics.django_http_responses_total_by_status_total?.find(m => m.labels.status === '200')?.value || 0,
                metrics.django_http_responses_total_by_status_total?.find(m => m.labels.status === '300')?.value || 0,
                metrics.django_http_responses_total_by_status_total?.find(m => m.labels.status === '400')?.value || 0,
                metrics.django_http_responses_total_by_status_total?.find(m => m.labels.status === '500')?.value || 0
            ];
            charts.httpStatus.update();

            const latencyBuckets = metrics.django_http_requests_latency_including_middlewares_seconds_bucket || [];
            const latencyData = latencyBuckets.filter(b => parseFloat(b.labels.le) < Infinity).map(b => b.value);
            if (latencyData.length > 0) {
                charts.latencyChart.data.labels = latencyBuckets.map(b => `${parseFloat(b.labels.le) * 1000}ms`);
                charts.latencyChart.data.datasets[0].data = latencyData;
                charts.latencyChart.update();
            }

            const requestPayloadSum = metrics.django_http_requests_body_total_bytes_sum?.[0]?.value || 0;
            const responsePayloadSum = metrics.django_http_responses_body_total_bytes_sum?.[0]?.value || 0;
            charts.payloadChart.data.datasets[0].data = [
                (requestPayloadSum / 1024).toFixed(2),
                (responsePayloadSum / 1024).toFixed(2)
            ];
            charts.payloadChart.update();

            charts.cacheHitsMisses.data.datasets[0].data = [
                metrics.django_cache_get_hits_total?.[0]?.value || 0,
                metrics.django_cache_get_misses_total?.[0]?.value || 0
            ];
            charts.cacheHitsMisses.update();

            charts.cacheOperations.data.datasets[0].data = [
                metrics.django_cache_get_total?.[0]?.value || 0,
                metrics.django_cache_set_total?.[0]?.value || 0,
                metrics.django_cache_delete_total?.[0]?.value || 0
            ];
            charts.cacheOperations.update();

            // Update Requests by View Chart
            if (metrics.django_http_requests_total_by_view_transport_method_total) {
                let reqByView = {};
                metrics.django_http_requests_total_by_view_transport_method_total.forEach(item => {
                    const view = item.labels.view;
                    reqByView[view] = (reqByView[view] || 0) + item.value;
                });
                // Sort by request count descending and take top 10
                const reqByViewSorted = Object.entries(reqByView)
                    .sort((a, b) => b[1] - a[1])
                    .slice(0, 10);
                charts.requestsByViewChart.data.labels = reqByViewSorted.map(item => item[0]);
                charts.requestsByViewChart.data.datasets[0].data = reqByViewSorted.map(item => item[1]);
                charts.requestsByViewChart.update();
            }

            // Update Top 10 Slowest Views Chart
            // We use the count and sum metrics for latency per view to compute an average.
            if (metrics['django_http_requests_latency_seconds_by_view_method_count'] &&
                metrics['django_http_requests_latency_seconds_by_view_method_sum']) {
                let responseTimeByView = {};
                // Aggregate counts by view
                metrics['django_http_requests_latency_seconds_by_view_method_count'].forEach(item => {
                    const view = item.labels.view;
                    responseTimeByView[view] = responseTimeByView[view] || { count: 0, sum: 0 };
                    responseTimeByView[view].count += item.value;
                });
                // Aggregate sums by view
                metrics['django_http_requests_latency_seconds_by_view_method_sum'].forEach(item => {
                    const view = item.labels.view;
                    responseTimeByView[view] = responseTimeByView[view] || { count: 0, sum: 0 };
                    responseTimeByView[view].sum += item.value;
                });
                // Compute average latency in ms per view
                let avgResponseTime = [];
                for (let view in responseTimeByView) {
                    let data = responseTimeByView[view];
                    if (data.count > 0) {
                        let avgMs = (data.sum / data.count) * 1000;
                        avgResponseTime.push([view, avgMs]);
                    }
                }
                // Sort descending (slowest first) and take top 10
                avgResponseTime.sort((a, b) => b[1] - a[1]);
                avgResponseTime = avgResponseTime.slice(0, 10);
                charts.slowViewsChart.data.labels = avgResponseTime.map(item => item[0]);
                charts.slowViewsChart.data.datasets[0].data = avgResponseTime.map(item => parseFloat(item[1].toFixed(2)));
                charts.slowViewsChart.update();
            }

        }

        // Initial fetch and periodic updates
        fetchMetrics();
        setInterval(fetchMetrics, 5000);
    });
</script>
{% endblock %}