-r production.txt

Werkzeug[watchdog]==3.0.3 # https://github.com/pallets/werkzeug
ipdb==0.13.13  # https://github.com/gotcha/ipdb
psycopg[binary]==3.2.1  # https://github.com/psycopg/psycopg

# Testing
# ------------------------------------------------------------------------------
mypy==1.11.1  # https://github.com/python/mypy
django-stubs[compatible-mypy]==5.0.4  # https://github.com/typeddjango/django-stubs
pytest==8.3.2  # https://github.com/pytest-dev/pytest
pytest-sugar==1.0.0  # https://github.com/Frozenball/pytest-sugar
djangorestframework-stubs==3.15.0  # https://github.com/typeddjango/djangorestframework-stubs

# Documentation
# ------------------------------------------------------------------------------
sphinx==7.4.7  # https://github.com/sphinx-doc/sphinx
sphinx-autobuild==2024.4.16 # https://github.com/GaretJax/sphinx-autobuild

# Code quality
# ------------------------------------------------------------------------------
ruff==0.5.7  # https://github.com/astral-sh/ruff
coverage==7.6.1  # https://github.com/nedbat/coveragepy
djlint==1.34.1  # https://github.com/Riverside-Healthcare/djLint
pre-commit==3.8.0  # https://github.com/pre-commit/pre-commit

# Django
# ------------------------------------------------------------------------------
factory-boy==3.3.0  # https://github.com/FactoryBoy/factory_boy

django-debug-toolbar==4.4.6  # https://github.com/jazzband/django-debug-toolbar
django-extensions==3.2.3  # https://github.com/django-extensions/django-extensions
django-coverage-plugin==3.1.0  # https://github.com/nedbat/django_coverage_plugin
pytest-django==4.8.0  # https://github.com/pytest-dev/pytest-django
django_silk==5.2.0

# Data migration
# ------------------------------------------------------------------------------
diskcache
pandas
sqlalchemy
tqdm

