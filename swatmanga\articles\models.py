from django.db import models
from swatmanga.common.fields import WebPVersatileImageField
from swatmanga.users.models import User
from taggit.managers import TaggableManager
from tinymce import models as tinymce_models
from swatmanga.common.utils import dash2underscore
from django.core.validators import FileExtensionValidator
from django.conf import settings
from slugify import slugify
import os


def article_cover_upload_to(instance, filename):
    # Builds the path: 'series/serie_name/cover.webp'
    return os.path.join("articles", dash2underscore(instance.slug), "cover.webp")


class Article(models.Model):
    title = models.CharField(max_length=255)
    slug = models.SlugField(max_length=255, unique=True, auto_created=True)
    content = tinymce_models.HTMLField()
    cover = WebPVersatileImageField(
        "Cover Image",
        upload_to=article_cover_upload_to,
        blank=False,
        null=False,
        lossy_quality=93,
        validators=[
            FileExtensionValidator(allowed_extensions=settings.ALLOWED_IMAGE_EXTENSIONS)
        ],
    )

    is_hot = models.BooleanField(default=False, db_default=False)
    tags = TaggableManager(blank=True)

    public = models.BooleanField(default=True, db_default=True)
    allow_comments = models.BooleanField("allow comments", default=True)

    # Records
    created_at = models.DateTimeField(auto_now_add=True)
    created_by: User = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="created_articles",
    )

    updated_at = models.DateTimeField(auto_now=True, null=True)
    updated_by: User = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="edited_articles",
    )

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(self.title)
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.title} at {self.created_at}"
