from allauth.account.decorators import secure_admin_login
from django.conf import settings
from django.contrib import admin
from django.contrib.auth import admin as auth_admin
from django.utils.translation import gettext_lazy as _
from django.utils.html import format_html
from django_admin_inline_paginator.admin import TabularInlinePaginated
from django import forms
from django.contrib import admin
from django.db.models import Exists, OuterRef, Q

from swatmanga.plans.models import PlanSubscription

from .forms import UserAdminChangeForm, UserAdminCreationForm
from .models import User


if settings.DJANGO_ADMIN_FORCE_ALLAUTH:
    # Force the `admin` sign in process to go through the `django-allauth` workflow:
    # https://docs.allauth.org/en/latest/common/admin.html#admin
    admin.autodiscover()
    admin.site.login = secure_admin_login(admin.site.login)  # type: ignore[method-assign]

class VIPStatusFilter(admin.SimpleListFilter):
    title = "VIP Status"  # Display name in the admin
    parameter_name = "vip"  # URL query parameter

    def lookups(self, request, model_admin):
        # Define filter options
        return (
            ("yes", "VIP Users"),
            ("no", "Non-VIP Users"),
        )

    def queryset(self, request, queryset):
        # Filter logic based on selection
        if self.value() == "yes":
            return queryset.filter(
                Exists(
                    PlanSubscription.objects.filter(
                        user=OuterRef("pk"),
                        status=PlanSubscription.PlanSubscriptionStatus.SUBSCRIBED,
                    )
                )
            )
        elif self.value() == "no":
            return queryset.filter(
                ~Exists(
                    PlanSubscription.objects.filter(
                        user=OuterRef("pk"),
                        status=PlanSubscription.PlanSubscriptionStatus.SUBSCRIBED,
                    )
                )
            )
        return queryset

class ShowAdsFilter(admin.SimpleListFilter):
    title = "Show Ads"
    parameter_name = "ads_enabled"

    def lookups(self, request, model_admin):
        # Define filter options
        return (
            ("yes", "Will see Ads"),
            ("no", "Won't see Ads"),
        )

    def queryset(self, request, queryset):
        # Filter logic based on selection
        if self.value() == "yes":
            return queryset.filter(
                ~Exists(
                    PlanSubscription.objects.filter(
                        user=OuterRef("pk"),
                        status=PlanSubscription.PlanSubscriptionStatus.SUBSCRIBED,
                        plan__ads_enabled=True,
                    )
                )
            )
        elif self.value() == "no":
            return queryset.filter(
                Exists(
                    PlanSubscription.objects.filter(
                        user=OuterRef("pk"),
                        status=PlanSubscription.PlanSubscriptionStatus.SUBSCRIBED,
                        plan__ads_enabled=True,
                    )
                )
            )
        return queryset

class PlanSubscriptionInline(TabularInlinePaginated):
    model = PlanSubscription
    extra = 0 
    per_page = 10
    fields = ('plan', 'status', 'expiration_date', 'payment_receipt')
    readonly_fields = ('created_at', 'updated_at')
    ordering = ('-created_at',)  # Show newest first


@admin.register(User)
class UserAdmin(auth_admin.UserAdmin):
    inlines = [PlanSubscriptionInline]
    form = UserAdminChangeForm
    add_form = UserAdminCreationForm
    fieldsets = (
        (None, {"fields": ("username", "email", "password")}),
        (("Personal info"), {"fields": ("name", "coins", "avatar", "avatar_preview")}),
        (
            ("Permissions"),
            {
                "fields": (
                    "is_active",
                    "is_staff",
                    "is_superuser",
                    "groups",
                    "user_permissions",
                ),
            },
        ),
        (("Important dates"), {"fields": ("created_at", "updated_at", "last_login",)}),
    )
    list_display = ["email", "name", "coins", "vip", "ads_enabled"]
    readonly_fields = ("avatar_preview", "last_login", "created_at", "updated_at")
    search_fields = ["email", "username", "name"]
    list_filter = auth_admin.UserAdmin.list_filter + (VIPStatusFilter, ShowAdsFilter)
    ordering = ["id"]
    add_fieldsets = (
        (
            None,
            {
                "classes": ("wide",),
                "fields": ("email", "username", "name", "password1", "password2"),
            },
        ),
    )

    def vip(self, obj):
        return obj.vip
    vip.boolean = True
    vip.short_description = "VIP" 

    def ads_enabled(self, obj):
        return not obj.ads_enabled
    ads_enabled.boolean = True
    ads_enabled.short_description = "Show Ads"

    def avatar_preview(self, obj):
        if obj.avatar:
            return format_html(
                '<img src="{}" style="max-height: 200px;" />', 
                obj.avatar.crop['200x200'].url
            )
        return "-"
    avatar_preview.short_description = "Preview"
