from drf_spectacular.utils import extend_schema_serializer
from rest_framework import serializers

from swatmanga.users.api.serializers import UserSerializer
from swatmanga.users.models import User

from ...models import PaymentDetail, PaymentMethod, Plan, PlanFeature, PlanSubscription


class PlanSerializer(serializers.ModelSerializer):
    features = serializers.SlugRelatedField(
        queryset=PlanFeature.objects.all(), slug_field="text", many=True
    )
    params = serializers.ListSerializer(child=serializers.CharField())

    class Meta:
        model = Plan
        fields = [
            "id",
            "title",
            "short_description",
            "plan_type",
            "currency",
            "price",
            "params",
            "ads_enabled",
            "add_coins",
            "color",
            "features",
            "created_at",
        ]


class PlanSubscriptionSerializer(serializers.ModelSerializer):
    plan = PlanSerializer(read_only=True)
    user = UserSerializer(read_only=True)

    def create(self, validated_data):
        # Extract `user_id` and `plan_id`
        user = self.context.pop("user")
        plan = self.context.pop("plan")
        params = self.context.pop("params")

        validated_data["params"] = params
        validated_data["user"] = user
        validated_data["plan"] = plan

        # Create and return the PlanSubscription instance
        return super().create(validated_data)

    class Meta:
        model = PlanSubscription
        fields = [
            "id",
            "user",
            "user_discord",
            "plan",
            "payment_receipt",
            "status",
            "expiration_date",
            "is_active",
            "days_remaining",
            "created_at",
        ]
        extra_kwargs = {
            "status": {"read_only": True},
            "user_discord": {"read_only": True},
            "expiration_date": {"read_only": True},
        }


class UserSubscriptionSerializer(serializers.ModelSerializer):
    plan = PlanSerializer(read_only=True)

    class Meta:
        model = PlanSubscription
        fields = [
            "id",
            "plan",
            "payment_receipt",
            "status",
            "expiration_date",
            "is_active",
            "days_remaining",
            "created_at",
        ]
        extra_kwargs = {
            "status": {"read_only": True},
            "expiration_date": {"read_only": True},
        }


class PaymentDetailSerializer(serializers.ModelSerializer):
    class Meta:
        model = PaymentDetail
        fields = ("key", "value")


class PaymentMethodSerializer(serializers.ModelSerializer):
    details = PaymentDetailSerializer(many=True, read_only=True)

    class Meta:
        model = PaymentMethod
        fields = ("id", "name", "description", "details")
