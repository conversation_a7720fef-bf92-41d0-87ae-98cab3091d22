# Generated by Django 5.0.8 on 2025-04-04 08:38

import django.core.validators
import swatmanga.plans.models
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("plans", "0011_plan_add_coins_plan_duration_months_plan_plan_type_and_more"),
    ]

    operations = [
        migrations.AlterField(
            model_name="plansubscription",
            name="payment_receipt",
            field=models.ImageField(
                upload_to=swatmanga.plans.models.plan_subscription_upload_to,
                validators=[
                    django.core.validators.FileExtensionValidator(
                        allowed_extensions=("jpeg", "jpg", "webp", "png", "gif")
                    )
                ],
            ),
        ),
    ]
