# urls.py
from django.urls import path
from django_prometheus import exports as prometheus_exports
from django.http import HttpResponseForbidden
from django.conf import settings

def secure_metrics(request, *args, **kwargs):
    # Only allow requests from specific IP addresses (e.g., localhost or your Prometheus server)
    allowed_hosts = settings.PROMETHEUS_ALLOWED_HOSTS
    if request.get_host() not in allowed_hosts:
        user = request.user
        if not user or not (user.is_authenticated and user.is_staff):
            return HttpResponseForbidden("Forbidden")
    return prometheus_exports.ExportToDjangoView(request, *args, **kwargs)
