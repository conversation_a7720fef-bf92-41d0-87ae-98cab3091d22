
from fcm_django.api.rest_framework import FCMDeviceCreateOnlyViewSet, FCMDeviceSerializer

class CustomFCMDeviceSerialize(FCMDeviceSerializer):
    def save(self, **kwargs):
        # this new save method, handles logout automatically
        device = super().save(**kwargs)
        user = self.context["request"].user
        if user is None or not user.is_authenticated:
            device.user = None
            device.save()
        return device


class CustomFCMDeviceCreateOnlyViewSet(FCMDeviceCreateOnlyViewSet):
    serializer_class = CustomFCMDeviceSerialize

