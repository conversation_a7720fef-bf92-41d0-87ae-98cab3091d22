from django.db import migrations

from swatmanga.series.groups import SerieGroups


def create_initial_groups(apps, schema_editor):
    # Get the Group model from the apps registry
    Group = apps.get_model("auth", "Group")

    # Create the initial groups
    for g in SerieGroups:
        Group.objects.get_or_create(name=g)


def delete_initial_groups(apps, schema_editor):
    # Get the Group model from the apps registry
    Group = apps.get_model("auth", "Group")

    # Delete the initial groups
    for g in SerieGroups:
        Group.objects.filter(name=g).delete()


class Migration(migrations.Migration):
    dependencies = [
        ("series", "0001_initial"),
    ]

    operations = [
        migrations.RunPython(create_initial_groups, delete_initial_groups),
    ]
