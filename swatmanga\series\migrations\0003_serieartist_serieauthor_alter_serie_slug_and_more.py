# Generated by Django 5.0.8 on 2024-08-13 09:51

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("series", "0002_favorite"),
    ]

    operations = [
        migrations.CreateModel(
            name="SerieArtist",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100, unique=True)),
            ],
            options={
                "db_table": "serie_artists",
            },
        ),
        migrations.CreateModel(
            name="SerieAuthor",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100, unique=True)),
            ],
            options={
                "db_table": "serie_authors",
            },
        ),
        migrations.Alter<PERSON>ield(
            model_name="serie",
            name="slug",
            field=models.SlugField(auto_created=True, max_length=255, unique=True),
        ),
        migrations.AlterField(
            model_name="serie",
            name="artist",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.DO_NOTHING,
                to="series.serieartist",
            ),
        ),
        migrations.AlterField(
            model_name="serie",
            name="author",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.DO_NOTHING,
                to="series.serieauthor",
            ),
        ),
    ]
