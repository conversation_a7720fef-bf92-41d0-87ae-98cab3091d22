import os

from io import Bytes<PERSON>
from PIL import Image
from versatileimagefield.fields import VersatileI<PERSON><PERSON>ield
from django.core.files.base import ContentFile


class WebPVersatileImageField(VersatileImageField):
    def __init__(self, *args, lossy_quality=100, **kwargs):
        self.lossy_quality = lossy_quality
        super().__init__(*args, **kwargs)

    def save_form_data(self, instance, data):
        if data and hasattr(data, "image"):
            # Delete the old file if it exists
            if instance.pk:  # Check if the instance is already saved (update scenario)
                try:
                    current_instance = instance.__class__.objects.get(pk=instance.pk)
                    old_file = getattr(current_instance, self.attname)
                    if old_file and old_file.name:
                        old_file.delete(save=False)  # Delete without saving the model
                except instance.__class__.DoesNotExist:
                    pass  # New instance, no old file to delete

            # Process and convert the new image
            img = Image.open(data)
            if img.format != "WEBP":
                output = BytesIO()
                img.save(output, format="WEBP", quality=self.lossy_quality)
                output.seek(0)
                # Create a new ContentFile with the converted image
                file_name = os.path.splitext(data.name)[0] + ".webp"
                data = ContentFile(output.read(), name=file_name)

        super().save_form_data(instance, data)