# Generated by Django 5.0.8 on 2024-09-08 13:22

import django.core.validators
from django.db import migrations

import swatmanga.common.fields
import swatmanga.users.models


class Migration(migrations.Migration):
    dependencies = [
        ("users", "0001_initial"),
    ]

    operations = [
        migrations.AddField(
            model_name="user",
            name="avatar",
            field=swatmanga.common.fields.WebPVersatileImageField(
                blank=True,
                null=True,
                upload_to=swatmanga.users.models.user_avatar_upload_to,
                validators=[
                    django.core.validators.FileExtensionValidator(
                        allowed_extensions=[".jpeg", ".jpg", ".webp", ".png", ".gif"]
                    )
                ],
                verbose_name="Avatar Image",
            ),
        ),
    ]
