import factory
from django.utils.text import slugify
from factory import Faker
from factory.django import DjangoModelFactory

from swatmanga.users.factories import UserFactory

from .models import Plan, PlanFeature, PlanSubscription


class PlanFactory(DjangoModelFactory):
    class Meta:
        model = Plan
        django_get_or_create = ("slug",)

    title = Faker("sentence", nb_words=3)
    slug = factory.LazyAttribute(lambda o: slugify(o.title))
    short_description = Faker("sentence", nb_words=6)
    currency = Faker("currency_code")  # Generates ISO 4217 currency code
    price = Faker(
        "pydecimal",
        left_digits=2,
        right_digits=2,
        positive=True,
        min_value=1,
        max_value=100,
    )
    color = Faker("hex_color")  # Generates hex color code
    created_at = Faker("date_time_this_decade")
    updated_at = Faker("date_time_this_decade")


class PlanFeatureFactory(DjangoModelFactory):
    class Meta:
        model = PlanFeature
        django_get_or_create = ("slug",)

    plan = factory.SubFactory(PlanFactory)
    text = Faker("sentence", nb_words=10)
    slug = factory.LazyAttribute(
        lambda o: slugify(o.text[:50])
    )  # Limit slug length to 50 characters
    created_at = Faker("date_time_this_decade")
    updated_at = Faker("date_time_this_decade")


class PlanSubscriptionFactory(DjangoModelFactory):
    class Meta:
        model = PlanSubscription

    user = factory.SubFactory(UserFactory)  # Assuming you have a UserFactory defined
    plan = factory.SubFactory(PlanFactory)
    user_discord = Faker("user_name")  # Generates a fake username for Discord handle
    payment_receipt = factory.django.ImageField(
        filename="receipt.webp", from_path="samples/sample_receipt.webp"
    )
    status = factory.Iterator(
        PlanSubscription.PlanSubscriptionStatus.values
    )  # Random status from available choices
    created_at = Faker("date_time_this_decade")
    updated_at = Faker("date_time_this_decade")
