from rest_framework.permissions import BasePermission, IsAuthenticated
from rest_framework.request import Request
from rest_framework.views import APIView

from swatmanga.users.utils import user_has_group

MANAGE_ARTICLES_GROUP = "manage_articles"
UPLOAD_ARTICLES_GROUP = "manage_articles"


class ManageArticlesPermission(BasePermission):
    """
    Custom permission to only allow users with the 'manage-articles' role to create, update, or delete series.
    """

    def has_permission(self, request: Request, view: APIView) -> bool:
        return request.user.is_authenticated and user_has_group(
            request.user, SerieGroups.MANAGE_SERIES
        )

    def has_object_permission(
        self, request: Request, view: APIView, obj: Serie
    ) -> bool:
        user = request.user
        if any([user.is_staff, user.is_superuser, obj.created_by == request.user]):
            return True
        return False

