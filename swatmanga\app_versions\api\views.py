from rest_framework import viewsets
from rest_framework.response import Response
from rest_framework.permissions import AllowAny
from ..models import AppVersions
from .serializers import AppVersionsSerializer
from drf_spectacular.utils import extend_schema
from rest_framework.decorators import action
from django.core.cache import cache

class AppVersionsViewSet(viewsets.ViewSet):
    queryset = AppVersions.objects.all()
    permission_classes = [AllowAny]

    @extend_schema(
        responses={200: AppVersionsSerializer},
    )
    @action(methods=["GET"], url_path='', detail=False)
    def version(self, request):
        cache_key = "app_version"
        cached_data = cache.get(cache_key)
        if cached_data:
            return Response(cached_data)

        app_versions = AppVersions.get_solo_instance()
        serializer = AppVersionsSerializer(app_versions)
        response = Response(serializer.data)
        cache.set(cache_key, response.data, 60 * 60 * 24)
        return response
