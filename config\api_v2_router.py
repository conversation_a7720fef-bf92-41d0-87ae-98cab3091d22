from django.conf import settings
from django.urls import path
from rest_framework.routers import <PERSON><PERSON>ult<PERSON><PERSON><PERSON>, SimpleRouter
from rest_framework_simplejwt.views import TokenObtainPairView, TokenRefreshView

from swatmanga.accounts.api.v1.views import AuthViewSet, EmailVerificationViewSet
from swatmanga.comments.api.v1.views import CommentViewSet
from swatmanga.common.api.fcm import CustomFCMDeviceCreateOnlyViewSet
from swatmanga.plans.api.v1.views import PaymentMethodViewSet, PlanViewSet
from swatmanga.series.api.v1.views import (
    ChapterViewSet,
    GenreViewSet,
    SerieStatusViewSet,
    SerieTypeViewSet,
    SerieArtistViewSet,
    SerieAuthorViewSet,
)
from swatmanga.series.api.v2.views import SerieViewSet
from swatmanga.users.api.views import UserViewSet
from swatmanga.ads.api.views import GoogleAdsViewSet
from swatmanga.app_versions.api.views import AppVersionsViewSet
from swatmanga.reports.api.views import ReportViewSet
from swatmanga.articles.api.views import ArticleViewSet
from swatmanga.announcments.api.views import AnnouncementsViewSet


from drf_spectacular.views import SpectacularAPIView


router = (
    DefaultRouter(use_regex_path=True)
    if settings.DEBUG
    else SimpleRouter(use_regex_path=True)
)

# TODO: consider basenames

router.register(r'auth', AuthViewSet, basename='auth')
router.register(r'verify-email', EmailVerificationViewSet, basename='verify-email')
router.register("users", UserViewSet)

router.register(r'ads', GoogleAdsViewSet)
router.register(r'announcements', AnnouncementsViewSet)
router.register(r'app-versions', AppVersionsViewSet)
router.register(r'articles', ArticleViewSet)
router.register("series", SerieViewSet)
router.register("chapters", ChapterViewSet)
router.register("series-types", SerieTypeViewSet)
router.register("series-statuses", SerieStatusViewSet)
router.register("series-authors", SerieAuthorViewSet)
router.register("series-artists", SerieArtistViewSet)
router.register("genres", GenreViewSet)
router.register("comments", CommentViewSet)

router.register("plans", PlanViewSet)
router.register(r"payment-methods", PaymentMethodViewSet)

router.register("devices", CustomFCMDeviceCreateOnlyViewSet)
router.register(r'reports', ReportViewSet)


app_name = "v2"
urlpatterns = router.urls + [
    path("token/", TokenObtainPairView.as_view(), name="token_obtain_pair"),
    path("token/refresh/", TokenRefreshView.as_view(), name="token_refresh"),
    path("v2/schema/", SpectacularAPIView.as_view(api_version='v2'), name="api-schema"),
]

