import re

from firebase_admin.messaging import Message, Notification, APNSConfig, Aps, APNSPayload
from urllib.parse import parse_qs, u<PERSON><PERSON><PERSON>, urljoin
from django.core.cache import cache
from django.conf import settings

from django.contrib.sites.models import Site


def _get_placeholders(text):
    """finds all placeholders like @(variable(.attrs)?: format)"""

    placeholders = re.findall(r"@\(.*?\)", text)
    return placeholders


def _parse_placeholder(placeholder) -> tuple[list[str], str]:
    """parses placeholders to variable.attrs, formatting_spec"""

    match = re.match(r"@\(([a-zA-Z0-9\._\|]+):?\s*(.*)\)", placeholder)
    variable_names = match.group(1)
    format_spec = match.group(2)

    variable_names = variable_names.split("|")

    return variable_names, format_spec


def format_using_attributes(text, obj):
    """parses text placeholders and replace them with values"""
    if text is None:
        return None

    placeholders = _get_placeholders(text)
    formatted_text = str(text)

    # Iterate over found placeholders
    for placeholder in placeholders:
        variable_names, format_spec = _parse_placeholder(placeholder)

        attribute_value = obj
        for variable_name in variable_names:
            # Get attribute value from the object
            variable_name_nested_attrs = variable_name.split(".")
            attribute_value2 = attribute_value
            while attribute_value2 is not None and variable_name_nested_attrs:
                attr = variable_name_nested_attrs.pop(0)
                attribute_value2 = getattr(attribute_value, attr)
                if attribute_value2 is not None:
                    attribute_value = attribute_value2
            if attribute_value2 is not None:
                break

        if attribute_value is None:
            raise RuntimeError("Can't be a null value")

        # Apply formatting if specified
        if format_spec:
            try:
                format_string = "{attribute_value:" + format_spec + "}"
                formatted_value = format_string.format(attribute_value=attribute_value)
            except ValueError:
                formatted_value = attribute_value
        else:
            formatted_value = attribute_value

        # Replace placeholder with formatted value
        formatted_text = formatted_text.replace(placeholder, str(formatted_value))

    return formatted_text


def get_formated_fcm_notification(
    instance, *, title, body, image=None, payload=None, **kwargs
):
    title = format_using_attributes(title, instance)
    body = format_using_attributes(body, instance)
    aps = Aps(badge=0)
    apns_config = None
    if payload:
        apns_payload = APNSPayload(aps=aps, **payload)
        apns_config = APNSConfig(payload=apns_payload)
    return Message(
        data=payload,
        apns=apns_config,
        notification=Notification(title=title, body=body, image=image),
        **kwargs,
    )


def dash2underscore(string: str) -> str:
    """replaces dashes `-` with underscores `_`,
    useful to make universal directories out of slug strings"""
    return string.replace("-", "_")


def make_cache_key_user_unique(request, key):
    return f'{key}_user_{request.user.id if request.user.is_authenticated else "anon"}'


# TODO: consider cache versioning
def generate_cache_key(request, prefix):
    query_params = request.GET.copy()
    sorted_params = sorted(query_params.items())
    encoded_params = urlencode(sorted_params)
    if encoded_params:
        return f"{prefix}_{encoded_params}"
    else:
        return f"{prefix}"


def get_current_site_url() -> str:
    s = Site.objects.get_current()
    url = "https://" + s.domain

    if settings.FORCE_SCRIPT_NAME:
        url = urljoin(url, settings.FORCE_SCRIPT_NAME)
    return url
