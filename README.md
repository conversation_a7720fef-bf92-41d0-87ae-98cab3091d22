# Swatmanga

>> Note: if `python manage.py runserver` freezes, it's a problem with python-magic and python-magic-bin version.

## Requirements
- postgres
- redis
- prometheus

## Docker
- Local development:
 
      $ docker compose -f docker-compose.local.yaml up --build

- Production:
 
      $ docker compose -f docker-compose.production.yml up --build

## Basic Commands

### Setting Up Your Users

- To create a **normal user account**, just go to Sign Up and fill out the form. Once you submit it, you'll see a "Verify Your E-mail Address" page. Go to your console to see a simulated email verification message. Copy the link into your browser. Now the user's email should be verified and ready to go.

- To create a **superuser account**, use this command:

      $ python manage.py createsuperuser
 
- To create default users for testing purposes:

      $ python manage.py initaccounts
 
- To populate the database with fake data:

      $ python manage.py populate_fake_data

For convenience, you can keep your normal user logged in on Chrome and your superuser logged in on Firefox (or similar), so that you can see how the site behaves for both kinds of users.

### Mysql 2 Postgres migration:
    $ python .\scripts\mysql2postgres_migrator.py --mysql-url mysql+pymysql://<user>:<pwd>@<host>/<db> --postgres-url postgresql://<user>:<pwd>@localhost:5431/<db>

### Type checks

Running type checks with mypy:

    $ mypy swatmanga

### Test coverage

To run the tests, check your test coverage, and generate an HTML coverage report:

    $ coverage run -m pytest
    $ coverage html
    $ open htmlcov/index.html

#### Running tests with pytest

    $ pytest

