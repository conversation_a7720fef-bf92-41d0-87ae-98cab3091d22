from rest_framework.permissions import BasePermission, IsAuthenticated
from rest_framework.request import Request
from rest_framework.views import APIView

from swatmanga.series.groups import SerieGroups
from swatmanga.series.models import Serie
from swatmanga.users.utils import user_has_group


class ManageSeriesPermission(BasePermission):
    """
    Custom permission to only allow users with the 'manage-series' role to create, update, or delete series.
    """

    def has_permission(self, request: Request, view: APIView) -> bool:
        return request.user.is_authenticated and user_has_group(
            request.user, SerieGroups.MANAGE_SERIES
        )

    def has_object_permission(
        self, request: Request, view: APIView, obj: Serie
    ) -> bool:
        user = request.user
        if not user.is_authenticated:
            return False
        if any([user.is_staff, user.is_superuser, obj.created_by == request.user]):
            return True
        return False


class ManageChaptersPermission(BasePermission):
    """
    Custom permission to only allow users with the 'manage-chapters' role to create, update, or delete series.
    """

    def has_permission(self, request: Request, view: APIView) -> bool:
        # Check if the user is authenticated and has the 'manage-chapters' role
        return request.user.is_authenticated and user_has_group(
            request.user, SerieGroups.MANA<PERSON>_CHAPTERS
        )

    def has_object_permission(
        self, request: Request, view: APIView, obj: Serie
    ) -> bool:
        user = request.user
        if not user.is_authenticated:
            return False
        if any([user.is_staff, user.is_superuser, obj.created_by == request.user]):
            return True
        return False

class UploadSeriesPermission(BasePermission):

    def has_permission(self, request: Request, view: APIView) -> bool:
        return request.user.is_authenticated and user_has_group(
            request.user, SerieGroups.UPLOAD_SERIES
        )

    def has_object_permission(
        self, request: Request, view: APIView, obj: Serie
    ) -> bool:
        user = request.user
        if not user.is_authenticated:
            return False
        if any([user.is_staff, user.is_superuser, obj.created_by == request.user]):
            return True
        return False


class UploadChaptersPermission(BasePermission):

    def has_permission(self, request: Request, view: APIView) -> bool:
        return request.user.is_authenticated and user_has_group(
            request.user, SerieGroups.UPLOAD_CHAPTERS
        )

    def has_object_permission(
        self, request: Request, view: APIView, obj: Serie
    ) -> bool:
        user = request.user
        if not user.is_authenticated:
            return False
        if any([user.is_staff, user.is_superuser, obj.created_by == request.user]):
            return True
        return False

