from django.contrib import admin
from .models import Article
from django.utils.html import format_html

@admin.register(Article)
class ArticleAdmin(admin.ModelAdmin):
    list_display = (
        "title", 
        "is_hot", 
        "public", 
        "created_at", 
        "author_info"
    )
    list_select_related = ("created_by", "updated_by")
    prepopulated_fields = {'slug': ('title',)}
    readonly_fields = (
        "created_at",
        "created_by",
        "updated_at",
        "updated_by",
        "cover_preview"
    )
    search_fields = ("title", "content", "created_by__username", "tags__name")
    list_filter = (
        "is_hot", 
        "public", 
        "allow_comments",
        "created_at",
    )
    autocomplete_fields = ["created_by", "updated_by", "tags"]
    date_hierarchy = "created_at"
    ordering = ("-created_at",)
    show_full_result_count = False  # Better performance with large datasets

    fieldsets = (
        ("Core Content", {
            'fields': (
                "title",
                "slug",
                "content",
            ),
            'classes': ("wide",),
        }),
        ("Media & SEO", {
            'fields': (
                "tags",
                "cover",
                "cover_preview",
            ),
            'classes': ("collapse",),
        }),
        ("Visibility & Moderation", {
            'fields': (
                "is_hot",
                "public",
                "allow_comments",
            )
        }),
        ("Audit Information", {
            'fields': (
                ("created_by", "created_at"),
                ("updated_by", "updated_at"),
            ),
            'classes': ("collapse",),
        }),
    )

    def cover_preview(self, obj):
        if obj.cover:
            return format_html(
                '<img src="{}" style="max-height: 200px;" />', 
                obj.cover.url
            )
        return "-"
    cover_preview.short_description = "Preview"

    def author_info(self, obj):
        if obj.created_by:
            return f"{obj.created_by.username} ({obj.created_by.email})"
        return "-"
    author_info.short_description = "Author"

    def save_model(self, request, obj, form, change):
        # Set created_by only when the object is created (not on update)
        # TODO: add auto updated_by
        if not change:
            obj.created_by = request.user
        super().save_model(request, obj, form, change)

    def get_queryset(self, request):
        return super().get_queryset(request).select_related(
            "created_by", "updated_by"
        ).prefetch_related("tags")