from swatmanga.common.utils import (
    format_using_attributes,
    get_formated_fcm_notification,
)
from django.contrib.sites.models import Site
from django.db import connection
from django.db.models.signals import post_delete, post_save
from django.dispatch import Signal, receiver
from django.dispatch import receiver
from django.core.cache import cache
from django_redis import get_redis_connection
from fcm_django.models import FC<PERSON>evice
from threading import Thread

from .models import Chapter, ChapterRead, Donation, Follow, Serie, Favorite
from .notifications import MESSAGES, TOPICS
from .signals import *


@receiver(post_save, sender=Chapter)
def update_and_refresh_materialized_view(sender, instance: Chapter, **kwargs):
    # Update latest_chapter_updated_at
    instance.serie.latest_chapter_updated_at = instance.updated_at
    instance.serie.save(update_fields=["latest_chapter_updated_at"])

    # Refresh the materialized view
    with connection.cursor() as cursor:
        cursor.execute("REFRESH MATERIALIZED VIEW releases;")


@receiver(post_delete, sender=Chapter)
def refresh_materialized_view(sender, **kwargs):
    # Refresh the materialized view
    with connection.cursor() as cursor:
        cursor.execute("REFRESH MATERIALIZED VIEW releases;")


@receiver(post_delete, sender=Serie)
def refresh_materialized_view2(sender, **kwargs):
    # Refresh the materialized view
    with connection.cursor() as cursor:
        cursor.execute("REFRESH MATERIALIZED VIEW releases;")


@receiver(post_save, sender=Follow)
def subscribe_user_to_serie_topic(sender, instance: Follow, created: bool, **kwargs):
    if not created:
        return

    topic = format_using_attributes(TOPICS.FOLLOW, instance)
    device = FCMDevice.objects.filter(user=instance.user)
    device.handle_topic_subscription(True, topic=topic)


@receiver(post_delete, sender=Follow)
def unsubscribe_user_from_serie_topic(sender, instance: Follow, **kwargs):
    topic = format_using_attributes(TOPICS.FOLLOW, instance)
    device = FCMDevice.objects.filter(user=instance.user)
    device.handle_topic_subscription(False, topic=topic)


@receiver(post_save, sender=Chapter)
def notify_new_chapter(sender, instance: Chapter, created: bool, **kwargs):
    if not created:
        return

    topic_name = TOPICS.NEW_CHAPTER
    topic = format_using_attributes(topic_name, instance)
    message = MESSAGES[topic_name]
    # serie_poster = instance.serie.poster.url if instance.serie.poster else None
    message = get_formated_fcm_notification(
        instance,
        **message,
        payload={
            "topic": "new_chapter",
            "chapter_id": str(instance.id),
            "serie_id": str(instance.serie.id),
        },
    )
    FCMDevice.send_topic_message(message, topic_name=topic)


@receiver(post_save, sender=Serie)
def notify_for_new_serie(sender, instance: Serie, created: bool, **kwargs):
    if not created:
        return

    topic_name = TOPICS.NEW_SERIE
    topic = format_using_attributes(topic_name, instance)
    message = MESSAGES[topic_name]
    # serie_poster = instance.poster.url if instance.poster else None
    message = get_formated_fcm_notification(
        instance,
        **message,
        payload={"serie_id": str(instance.id), "topic": "new_serie"},
    )
    FCMDevice.objects.all().send_message(message)


@receiver(post_save, sender=Donation)
def notify_for_donations(sender, instance: Donation, created: bool, **kwargs):
    if created:
        pass


# Cache invalidation
# TODO: optimize
# TODO: replace get_redis_connection with the imported cache object
@receiver([post_save, post_delete], sender=Serie)
def invalidate_serie_cache(sender, instance, **kwargs):
    redis_conn = get_redis_connection("default")
    # Invalidate serie retrieve
    keys = (
        redis_conn.keys(f"*serie_retrieve_{instance.pk}*")
        + redis_conn.keys(f"*serie_list*")
        + redis_conn.keys(f"*serie_releases*")
    )
    if keys:
        redis_conn.delete(*keys)


@receiver([post_save, post_delete], sender=Chapter)
def invalidate_chapter_cache(sender, instance, **kwargs):
    redis_conn = get_redis_connection("default")

    keys = []
    # Invalidate chapter retrieve and chapter images
    keys += redis_conn.keys(f"*chapters_retrieve_{instance.pk}*")
    keys += redis_conn.keys(f"*chapter_images_{instance.pk}*")
    keys += redis_conn.keys(f"*serie_{instance.serie_id}_chapters*")
    # Invalidate serie's chapters and retrieve
    keys += redis_conn.keys(f"*serie_retrieve_{instance.serie_id}*")
    keys += redis_conn.keys(f"*serie_releases*")

    if keys:
        redis_conn.delete(*keys)


@receiver([post_save, post_delete], sender=Favorite)
@receiver([post_save, post_delete], sender=Follow)
def invalidate_user_actions_cache(sender, instance, **kwargs):
    redis_conn = get_redis_connection("default")
    user_id = instance.user_id

    # Invalidate user-specific cache
    # TODO: incorrect
    keys = redis_conn.keys(f"*_user_{user_id}*")

    if keys:
        redis_conn.delete(*keys)


@receiver([post_save, post_delete], sender=ChapterRead)
def invalidate_user_history_cache(sender, instance, **kwargs):
    redis_conn = get_redis_connection("default")
    user_id = instance.user_id

    keys = redis_conn.keys(f"*chapters_history*_user_{user_id}*")
    if keys:
        redis_conn.delete(*keys)
