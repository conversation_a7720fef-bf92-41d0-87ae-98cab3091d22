from django.contrib import admin

from .models import PaymentDetail, PaymentMethod, Plan, PlanFeature, PlanSubscription


# Inline for PaymentDetail in PaymentMethod admin
class PaymentDetailInline(admin.TabularInline):
    model = PaymentDetail
    extra = 1
    fields = ("key", "value")
    verbose_name = "Payment Detail"
    verbose_name_plural = "Payment Details"


@admin.register(PaymentMethod)
class PaymentMethodAdmin(admin.ModelAdmin):
    list_display = ("name", "description")
    search_fields = ("name",)
    inlines = [PaymentDetailInline]

    def get_fieldsets(self, request, obj=None):
        fieldsets = super().get_fieldsets(request, obj)
        fieldsets[0][1]["fields"] = ("name", "description")
        return fieldsets


# Inline for PlanFeature in Plan admin
class PlanFeatureInline(admin.TabularInline):
    model = PlanFeature
    extra = 1
    fields = ("text",)
    verbose_name = "Feature"
    verbose_name_plural = "Features"


@admin.register(Plan)
class PlanAdmin(admin.ModelAdmin):
    list_display = ("title", "currency", "price", "color", "created_at", "updated_at")
    search_fields = ("title", "currency")
    list_filter = ("created_at", "updated_at")
    prepopulated_fields = {"slug": ("title",)}
    inlines = [PlanFeatureInline]
    readonly_fields = ("created_at", "updated_at")

    fieldsets = (
        (
            None,
            {
                "fields": (
                    "title",
                    "slug",
                    "short_description",
                    "plan_type",
                    "currency",
                    "price",
                    "ads_enabled",
                    "duration_months",
                    "add_coins",
                    "params",
                    "color",
                )
            },
        ),
        ("Timestamps", {"fields": ("created_at", "updated_at")}),
    )


@admin.register(PlanFeature)
class PlanFeatureAdmin(admin.ModelAdmin):
    list_display = ("plan", "text", "created_at", "updated_at")
    search_fields = ("text",)
    list_filter = ("created_at", "updated_at")
    prepopulated_fields = {"slug": ("text",)}
    readonly_fields = ("created_at", "updated_at")

    fieldsets = (
        (None, {"fields": ("plan", "text", "slug")}),
        ("Timestamps", {"fields": ("created_at", "updated_at")}),
    )


@admin.register(PlanSubscription)
class PlanSubscriptionAdmin(admin.ModelAdmin):
    list_display = (
        "user",
        "plan",
        "status",
        "created_at",
        "updated_at",
        "user_discord",
    )
    list_filter = ("status", "created_at", "updated_at")
    search_fields = ("user__username", "user_discord", "plan__title")
    readonly_fields = ("created_at", "updated_at", "user", "plan", "user_discord", "params", "payment_receipt")
    fieldsets = (
        (
            None,
            {"fields": ("user", "user_discord", "plan", "payment_receipt", "expiration_date", "status")},
        ),
        ("Request Info", {"fields": ("created_at", "updated_at", "params")}),
    )
