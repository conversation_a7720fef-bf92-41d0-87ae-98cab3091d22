from django.core.management.base import BaseCommand
from django.db.models import Max

from swatmanga.series.models import Chapter, Serie


class Command(BaseCommand):
    help = "Refresh latest_chapter_updated_at for all series."

    def handle(self, *args, **kwargs):
        # Get all series
        series = Serie.objects.all()

        # Iterate over each series to refresh latest_chapter_updated_at
        for serie in series:
            # Get the most recent chapter's updated_at timestamp
            latest_chapter = Chapter.objects.filter(serie=serie).aggregate(
                Max("updated_at")
            )
            latest_updated_at = latest_chapter["updated_at__max"]

            if latest_updated_at:
                serie.latest_chapter_updated_at = latest_updated_at
                serie.save()
                self.stdout.write(
                    self.style.SUCCESS(
                        f"Successfully updated {serie.title} latest_chapter_updated_at to {latest_updated_at}"
                    )
                )
            else:
                self.stdout.write(
                    self.style.WARNING(f"No chapters found for series: {serie.title}")
                )
