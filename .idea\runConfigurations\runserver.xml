<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="runserver" type="Python.DjangoServer" factoryName="Django server" singleton="true">
    <module name="swatmanga" />
    <option name="INTERPRETER_OPTIONS" value="" />
    <option name="PARENT_ENVS" value="true" />
    <envs>
      <env name="PYTHONUNBUFFERED" value="1" />
      <env name="DJANGO_SETTINGS_MODULE" value="config.settings.local" />
    </envs>
    <option name="SDK_HOME" value="" />
    <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
    <option name="IS_MODULE_SDK" value="true" />
    <option name="ADD_CONTENT_ROOTS" value="true" />
    <option name="ADD_SOURCE_ROOTS" value="true" />
    <PathMappingSettings>
      <option name="pathMappings">
        <list>
          <mapping local-root="$PROJECT_DIR$" remote-root="/app" />
        </list>
      </option>
    </PathMappingSettings>
    <option name="launchJavascriptDebuger" value="false" />
    <option name="port" value="8000" />
    <option name="host" value="0.0.0.0" />
    <option name="additionalOptions" value="" />
    <option name="browserUrl" value="" />
    <option name="runTestServer" value="false" />
    <option name="runNoReload" value="false" />
    <option name="useCustomRunCommand" value="false" />
    <option name="customRunCommand" value="" />
    <method />
  </configuration>
</component>
