# Generated by Django 5.0.8 on 2025-03-02 09:36

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("plans", "0010_alter_plansubscription_status"),
    ]

    operations = [
        migrations.AddField(
            model_name="plan",
            name="add_coins",
            field=models.PositiveIntegerField(
                default=0,
                help_text="Coins to add immediately when purchased (one-time plans only)",
            ),
        ),
        migrations.AddField(
            model_name="plan",
            name="duration_months",
            field=models.PositiveIntegerField(
                blank=True, help_text="Required for subscriptions", null=True
            ),
        ),
        migrations.AddField(
            model_name="plan",
            name="plan_type",
            field=models.CharField(
                choices=[
                    ("subscription", "Recurring Subscription"),
                    ("one_time", "One-Time Purchase"),
                ],
                default="subscription",
                max_length=20,
            ),
        ),
        migrations.AddField(
            model_name="plansubscription",
            name="expiration_date",
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.Alter<PERSON>ield(
            model_name="plansubscription",
            name="status",
            field=models.CharField(
                choices=[
                    ("pending", "Pending"),
                    ("subscribed", "Subscribed"),
                    ("canceled", "Canceled"),
                    ("expired", "Expired"),
                ],
                default="pending",
                max_length=20,
            ),
        ),
    ]
