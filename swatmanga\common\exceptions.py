# exceptions.py
from django.db import IntegrityError
from rest_framework.views import exception_handler
from rest_framework.response import Response
from rest_framework import status

def custom_exception_handler(exc, context):
    if isinstance(exc, IntegrityError):
        data = {'detail': str(exc)}
        return Response(data, status=status.HTTP_422_UNPROCESSABLE_ENTITY)

    return exception_handler(exc, context)    
