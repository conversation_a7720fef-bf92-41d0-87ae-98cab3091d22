#!/bin/bash
set -eo pipefail

# Color definitions
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
ENV="production"
APP_NAME="swatmanga"
DEPLOY_USER="qubit"
LOG_DIR="/var/log/${APP_NAME}"
BUILD_DIR="/tmp/${APP_NAME}_build"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
COMMIT_HASH=$(git rev-parse --short HEAD 2>/dev/null || echo "unknown")

# Initialize logging
exec > >(tee -a "${LOG_DIR}/deploy_${TIMESTAMP}.log") 2>&1

error_handler() {
    echo -e "${RED}ERROR:${NC} Deployment failed at line $1"
    slack_notification "Deployment of ${APP_NAME} (${COMMIT_HASH}) to ${ENV} FAILED"
    exit 1
}

slack_notification() {
    # Implement your Slack webhook integration here
    echo -e "${CYAN}SLACK:${NC} $1"  # Placeholder for actual notification
}

trap 'error_handler $LINENO' ERR

echo -e "${GREEN}=== Starting ${APP_NAME} ${ENV} deployment (${COMMIT_HASH}) ===${NC}"

# Sanity checks
echo -e "${BLUE}## Running pre-deployment checks${NC}"
[ $(id -u) -eq 0 ] && { echo -e "${RED}ERROR:${NC} Don't deploy as root"; exit 1; }
[ -d ".venv" ] || { echo -e "${RED}ERROR:${NC} Virtual environment missing"; exit 1; }
which uv >/dev/null || { echo -e "${RED}ERROR:${NC} uv not installed"; exit 1; }

# Maintenance mode
# echo "## Enabling maintenance mode"
# python manage.py maintenance_mode on

# Enable venv
source ./.venv/bin/activate

# Install dependencies
echo -e "${BLUE}## Installing requirements${NC}"
uv pip install -r requirements/${ENV}.txt

# Database migrations
echo -e "${BLUE}## Running database migrations${NC}"
python manage.py migrate --no-input

# Static files
echo -e "${BLUE}## Processing static files${NC}"
python manage.py collectstatic --noinput --clear
python manage.py compress --force

# Celery init
echo -e "${BLUE}## Init celery beat${NC}"
python manage.py init_celery_beat

# Systemd service management
echo -e "${BLUE}## Reloading system services${NC}"
sudo systemctl daemon-reload

# Database check
echo -e "${BLUE}## Verifying database connectivity${NC}"
python manage.py check --database default

# sudo systemctl restart swatapi.service
sudo systemctl restart swatapi@*

# Celery setup (if using)
echo -e "${BLUE}## Restarting background workers${NC}"
sudo systemctl restart celery_worker.service
sudo systemctl restart celery_beat.service

# Maintenance mode off
# echo "## Disabling maintenance mode"
# python manage.py maintenance_mode off

# Cleanup
echo -e "${BLUE}## Cleaning old deployments${NC}"
find "${LOG_DIR}" -name "deploy_*.log" -mtime +30 -delete

# Health check
echo -e "${BLUE}## Performing final health checks${NC}"
sleep 2
for port in {8000..8003}; do
    echo -e "${CYAN}Checking health on port $port${NC}"
    curl -sSf "http://localhost:${port}/healthz" >/dev/null || { 
        echo -e "${RED}Health check failed for port $port${NC}"; exit 1
    }
    echo -e "${GREEN}Port $port is healthy${NC}"
done
echo -e "${GREEN}All health checks passed successfully${NC}"

slack_notification "Deployment of ${APP_NAME} (${COMMIT_HASH}) to ${ENV} completed successfully"
# Disable venv
deactivate

echo -e "${GREEN}=== Deployment completed in ${SECONDS} seconds ===${NC}"