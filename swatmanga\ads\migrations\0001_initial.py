# Generated by Django 5.0.8 on 2025-02-02 09:08

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="GoogleAdLink",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "platform",
                    models.Char<PERSON>ield(
                        choices=[
                            ("web", "Web"),
                            ("android", "Android"),
                            ("iphone", "iPhone"),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    "ad_type",
                    models.Char<PERSON>ield(
                        choices=[
                            ("app", "App"),
                            ("banner", "Banner"),
                            ("interstitial", "Interstitial"),
                            ("rewarded", "Rewarded"),
                            ("native", "Native"),
                            ("video", "Video"),
                            ("search", "Search"),
                        ],
                        max_length=20,
                    ),
                ),
                ("key", models.<PERSON><PERSON><PERSON><PERSON>(help_text="The key for the ad")),
            ],
        ),
    ]
