from rest_framework import viewsets
from rest_framework.response import Response
from rest_framework.permissions import AllowAny
from ..models import Report
from .serializers import ReportSerializer
from drf_spectacular.utils import extend_schema
from rest_framework.decorators import action

class ReportViewSet(viewsets.GenericViewSet):
    queryset = Report.objects.all()
    permission_classes = [AllowAny]
    serializer_class = ReportSerializer

    def create(self, request):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid()
        serializer.save()

        return Response(serializer.data, 201)
