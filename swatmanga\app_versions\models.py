from django.db import models

class AppVersions(models.Model):
    latest_version = models.CharField(max_length=20, default="0.0.0")
    latest_version_link = models.URLField(blank=True, default="")
    minimum_supported_version = models.CharField(max_length=20, default="0.0.0")
    minimum_supported_version_link = models.URLField(blank=True, null=True)
    updated_at = models.DateTimeField(auto_now=True)

    def save(self, *args, **kwargs):
        self.pk = 1  # Ensuring there's only one record
        super().save(*args, **kwargs)

    def delete(self, *args, **kwargs):
        pass  # Prevent deletion

    @classmethod
    def get_solo_instance(cls):
        obj, created = cls.objects.get_or_create(pk=1)
        return obj
