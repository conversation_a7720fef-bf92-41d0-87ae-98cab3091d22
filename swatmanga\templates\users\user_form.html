{% extends "base.html" %}

{% load static %}

{% block title %}
  {{ user.name }}
{% endblock title %}

{% block content %}
  <div class="container">
    <h1>Edit Profile</h1>
    <div class="flex flex-col items-center my-4">

      <!-- Form -->
      <form method="post" action="{% url 'users:profile' %}" enctype="multipart/form-data" class="w-full max-w-md">
        {% csrf_token %}
        <!-- Render form fields manually -->
        {% for field in form %}
          <div class="mb-4">
            <!-- Label -->
            <label for="{{ field.id_for_label }}" class="block text-[var(--bs-gray-200)] font-semibold mb-2">
              {{ field.label }}
            </label>
            <!-- Special handling for avatar field -->
            {% if field.name == "avatar" %}
              <div class="flex flex-col gap-2">
                <!-- File Input -->
                <input type="file" name="{{ field.name }}" id="{{ field.id_for_label }}" accept="image/*" class="w-full" />
                <!-- Clear Checkbox (if avatar exists) -->
                {% if user.avatar %}
                  <label class="flex items-center text-[var(--bs-gray-400)]">
                    <input type="checkbox" name="avatar-clear" id="avatar-clear" class="custom-checkbox mr-2" />
                    Clear Avatar
                  </label>
                {% endif %}
              </div>
            {% else %}
              <!-- Other fields (e.g., name, email) -->
              {{ field }}
            {% endif %}
            <!-- Errors -->
            {% if field.errors %}
              <div class="text-[var(--anime-secondary)] text-sm mt-1">
                {% for error in field.errors %}
                  {{ error }}
                {% endfor %}
              </div>
            {% endif %}
          </div>
        {% endfor %}
        <button type="submit" class="w-full mt-4">Update</button>
      </form>
    </div>
  </div>
{% endblock content %}