from rest_framework import serializers

from swatmanga.common.api.serializers import CustomVersatileImageFieldSerializer
from swatmanga.users.models import User
from django.contrib.auth.models import Group


class UserSerializer(serializers.ModelSerializer[User]):
    avatar = CustomVersatileImageFieldSerializer(
        sizes="users_avatar", read_only=True
    )
    class Meta:
        model = User
        fields = ["id", "username", "name", "avatar"]

        extra_kwargs = {
            "url": {"view_name": "api:v1:user-detail", "lookup_field": "pk"},
            "name": {"required": True},
        }


class UserDetailsSerializer(serializers.ModelSerializer[User]):
    ads_enabled = serializers.SerializerMethodField()
    groups = serializers.SlugRelatedField(slug_field='name', many=True, read_only=True)

    class Meta:
        model = User
        fields = ["id", "username", "email", "name", "avatar", "coins", "ads_enabled", "is_staff", "is_superuser", "groups"]

        extra_kwargs = {
            "url": {"view_name": "api:v1:user-detail", "lookup_field": "pk"},
            "name": {"required": True},
        }

    def get_ads_enabled(self, obj: User) -> bool:
        return obj.ads_enabled


class UserWriteSerializer(UserDetailsSerializer):
    avatar = serializers.ImageField(write_only=True, required=False, allow_null=True)

    class Meta:
        model = User
        fields = ["id", "username", "email", "name", "avatar", "coins", "ads_enabled", "is_staff", "is_superuser", "groups"]
        extra_kwargs = {
            "username": {"read_only": True},
            "email": {"read_only": True},
            "coins": {"read_only": True},
            "ads_enabled": {"read_only": True},
            "is_staff": {"read_only": True},
            "is_superuser": {"read_only": True},
        }