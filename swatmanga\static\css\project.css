@tailwind base;
@tailwind components;
@tailwind utilities;

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

/* Custom Variables */
:root {
  --header-height: 75px;
  --footer-height: 160px;
  --header-footer-bg: #1a1f2b;
  --anime-primary: #00c4ff; /* Neon <PERSON>an */
  --anime-secondary: #ff00ff; /* Neon Magenta */
  --anime-accent: #7b00ff; /* Neon Purple */
  --bs-white: #fff;
  --bs-gray-100: #f8f9fa;
  --bs-gray-200: #e9ecef;
  --bs-gray-300: #dee2e6;
  --bs-gray-400: #ced4da;
  --bs-gray-500: #adb5bd;
  --bs-gray-600: #6c757d;
  --bs-gray-700: #495057;
  --bs-gray-800: #343a40;
  --bs-gray-900: #212529;
  --bs-border-width: 1px;
  --bs-border-radius: 0.5rem;
  --fontfamily: "Poppins", sans-serif !important;
}

/* Ensure html and body fill the entire viewport */
html, body {
  height: 100%;
  min-height: 100vh;
  margin: 0;
  padding: 0;
  -webkit-text-size-adjust: none;
}

* {
  outline: 0;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:focus {
  outline: none;
}

img {
  max-width: 100%;
  height: auto;
  border-radius: var(--bs-border-radius);
  transition: transform 0.3s ease;
  margin-bottom: 1rem; /* Add spacing below images */
}

img:hover {
  transform: scale(1.05);
}

a {
  color: var(--anime-primary);
  text-decoration: none;
  position: relative;
  transition: color 0.3s ease-in-out, text-shadow 0.3s ease-in-out;
  margin: 0 0.25rem; /* Add small horizontal spacing to links */
}

a::after {
  content: '';
  position: absolute;
  width: 0;
  height: 2px;
  bottom: -2px;
  left: 0;
  background: linear-gradient(90deg, var(--anime-primary), var(--anime-secondary));
  transition: width 0.3s ease-in-out;
}

a:hover {
  color: var(--bs-white);
  text-shadow: 0 0 8px var(--anime-primary);
}

a:hover::after {
  width: 100%;
}

button {
  transition: all 0.3s ease-in-out;
}

/* Updated Button Styling */
button, .custom-btn {
  display: inline-block;
  padding: 0.75rem 2rem;
  margin: 0.3rem 0.3rem; /* Consistent margin for buttons */
  background: linear-gradient(45deg, var(--anime-primary), var(--anime-secondary));
  color: var(--bs-white);
  border: 1px solid transparent;
  border-radius: var(--bs-border-radius);
  font-weight: 600;
  font-family: var(--fontfamily);
  box-shadow: 0 0 10px rgba(0, 196, 255, 0.5), inset 0 0 5px rgba(255, 0, 255, 0.3);
  transition: transform 0.2s ease, box-shadow 0.3s ease, background 0.3s ease, border-color 0.3s ease;
  cursor: pointer;
}

button:hover, .custom-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 0 20px rgba(0, 196, 255, 0.8), inset 0 0 8px rgba(255, 0, 255, 0.5);
  background: linear-gradient(45deg, var(--anime-secondary), var(--anime-primary));
  border-color: var(--anime-accent);
}

/* Input Styling */
input, input[type="text"], input[type="email"], input[type="password"], input[type="search"] {
  background: rgba(26, 31, 43, 0.9);
  color: var(--bs-gray-200);
  border: 1px solid var(--anime-primary);
  border-radius: var(--bs-border-radius);
  padding: 0.5rem 1rem;
  margin-bottom: 0.3rem; /* Add spacing below inputs */
  font-family: var(--fontfamily);
  font-size: 1rem;
  box-shadow: 0 0 5px rgba(0, 196, 255, 0.3);
  transition: border-color 0.3s ease, box-shadow 0.3s ease, background 0.3s ease;
}

input:hover, input:focus {
  background: rgba(40, 45, 55, 0.9);
  border-color: var(--anime-secondary);
  box-shadow: 0 0 10px rgba(255, 0, 255, 0.5);
}

input::placeholder {
  color: var(--bs-gray-500);
  opacity: 0.7;
}

/* Form Styling */
form {
  display: flex;
  flex-direction: column;
  gap: 0.3rem;
  max-width: 600px;
  margin: 0 auto;
}

form > div {
  display: grid;
  grid-template-columns: 1fr;
  gap: 0.3rem;
}

label {
  font-weight: 600;
  color: var(--bs-gray-300);
  display: block;
}


input, 
textarea,
select {
  width: 100%;
  background: rgba(26, 31, 43, 0.9);
  color: var(--bs-gray-200);
  border: 1px solid var(--anime-primary);
  border-radius: var(--bs-border-radius);
  padding: 0.75rem 1rem;
  font-family: var(--fontfamily);
  transition: all 0.3s ease;
}

textarea {
  min-height: 100px;
  resize: vertical;
}

input, 
textarea,
select {
  margin-bottom: 0; /* Remove individual margins */
}

select {
  appearance: none;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%2300c4ff'%3e%3cpath d='M7 10l5 5 5-5z'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 1rem center;
  background-size: 1em;
}


.errorlist {
  gap: 0.25rem;
  list-style: none;
  padding: 0;
  margin: 0;
  color: var(--anime-secondary);
  font-size: 0.9em;
  text-shadow: 0 0 8px rgba(255, 0, 255, 0.3);
}

.errorlist li {
  padding: 0.25rem;
  background: rgba(255, 0, 255, 0.1);
  border-radius: 0.25rem;
}


/* Checkbox and Radio Alignment */
input[type="checkbox"],
input[type="radio"] {
  width: 1.15em;
  height: 1.15em;
  margin: 0;
}

.input-group {
  display: flex;
  align-items: center;
  gap: 0.3rem;
}

.input-group label {
  margin-bottom: 0;
}

/* Form Button Container */
.form-actions {
  margin-top: 0.2rem;
  display: flex;
  gap: 0.3rem;
  justify-content: flex-end;
}

/* Focus States */
input:focus,
textarea:focus,
select:focus {
  background: rgba(40, 45, 55, 0.9);
  border-color: var(--anime-secondary);
  box-shadow: 0 0 10px rgba(255, 0, 255, 0.5);
}

/* List Styling */


ul li, ol li {
  list-style: none;
  margin-bottom: 0.3rem; /* Add spacing between list items */
}

/* Paragraph Styling */
p {
  font-size: 1rem;
  margin-bottom: 0.2rem; /* Add spacing below paragraphs */
  color: var(--bs-gray-300);
}

/* Heading Styling */
h1, h2, h3, h4, h5, h6 {
  margin-top: 1rem; /* Add spacing above headings */
  margin-bottom: 1rem; /* Add spacing below headings */
}

h1 {
  font-size: 2.5rem;
  font-weight: 700;
  background: linear-gradient(45deg, var(--anime-primary), var(--anime-secondary));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: 0 0 12px rgba(0, 196, 255, 0.5);
  animation: glow 2s ease-in-out infinite alternate;
}

h2 {
  font-size: 1.75rem;
  font-weight: 600;
  background: linear-gradient(45deg, var(--anime-accent), var(--anime-primary));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: 0 0 8px rgba(123, 0, 255, 0.4);
  animation: glow 3s ease-in-out infinite alternate;
}

h3 {
  font-size: 1.5rem;
  color: var(--bs-gray-200);
  font-weight: 600;
}

h4 {
  font-size: 1.25rem;
  color: var(--bs-gray-200);
  font-weight: 500;
}

h5 {
  font-size: 1.1rem;
  color: var(--bs-gray-200);
  font-weight: 500;
}

h6 {
  font-size: 1rem;
  color: var(--bs-gray-200);
  font-weight: 500;
}

/* General Block Elements */
div, section, article, aside {
  margin-bottom: 1rem; /* Add spacing below block elements */
}

/* Iframe Styling */
iframe {
  width: 100%;
  display: block;
  margin-bottom: 1rem; /* Add spacing below iframes */
}

/* Scrollbar Styling */
::-webkit-scrollbar {
  width: 6px;
  background: rgba(0, 0, 0, 0);
}

::-webkit-scrollbar-thumb {
  background: var(--anime-primary);
  border-radius: 3px;
}

/* Remove Outline on Focus */
input:hover,
input:focus,
button:hover,
button:focus,
.custom-btn:hover,
.custom-btn:focus {
  outline: none !important;
}

/* Body Styling */
body {
  width: 100vw;
  font-family: var(--fontfamily);
  font-size: 1rem;
  background: #0f1419;
  color: var(--bs-gray-300);
  line-height: 1.6;
  overflow-x: hidden;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Wrapper Styling */
.wrapper {
  min-height: 100vh;
  width: 100%;
  max-width: 100%;
  background: linear-gradient(145deg, rgba(15, 20, 25, 0.98), rgba(25, 30, 35, 0.98));
  display: flex;
  flex-direction: column;
}

/* Navigation */
nav {
  height: var(--header-height);
  max-height: var(--header-height);
  background: var(--header-footer-bg);
  box-shadow: 0 0 15px rgba(0, 196, 255, 0.2);
}

/* Container */
.container {
  max-width: 100%;
  margin: 1rem auto; /* Increased margin for better spacing */
  padding: 0 1rem;
  text-align: center;
  flex: 1;
}

/* Glowing Animation for Headings */
@keyframes glow {
  from {
    text-shadow: 0 0 8px rgba(0, 196, 255, 0.3), 0 0 12px rgba(0, 196, 255, 0.2);
  }
  to {
    text-shadow: 0 0 12px rgba(0, 196, 255, 0.6), 0 0 20px rgba(0, 196, 255, 0.4);
  }
}


.bg-header-footer {
  background-color: var(--header-footer-bg);
}

/* Alert Styling */
.alert {
  background: rgba(26, 31, 43, 0.9);
  color: var(--bs-gray-200);
  border: 1px solid var(--anime-primary);
  border-radius: var(--bs-border-radius);
  box-shadow: 0 0 10px rgba(0, 196, 255, 0.3);
  transition: opacity 0.3s ease;
  margin: 1rem 0; /* Add spacing around alerts */
  padding: 1rem; /* Ensure padding inside alerts */
}

.alert:hover {
  opacity: 0.95;
}

/* Responsive Adjustments */
@media (min-width: 768px) {
  form {
    gap: 0.3rem;
  }
  
  form > div {
    gap: 0.3rem;
  }

  .container {
    max-width: 720px;
    padding: 0;
  }

  h1 {
    font-size: 3.5rem;
  }

  h2 {
    font-size: 2.25rem;
  }


  input, input[type="text"], input[type="email"], input[type="password"], input[type="search"] {
    margin-bottom: 1rem; /* Slightly larger spacing on larger screens */
  }

  div, section, article, aside {
    margin-bottom: 1rem; /* Slightly larger spacing on larger screens */
  }
}

@media (min-width: 992px) {
  .container {
    max-width: 960px;
  }

  ::-webkit-scrollbar {
    width: 8px;
  }
}

@media (min-width: 1200px) {
  .container {
    max-width: 1220px;
  }
}
