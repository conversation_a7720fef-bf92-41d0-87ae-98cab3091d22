# Generated by Django 5.0.8 on 2025-04-04 08:38

import django.core.validators
import swatmanga.articles.models
import swatmanga.common.fields
from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ("articles", "0002_alter_article_tags"),
    ]

    operations = [
        migrations.AlterField(
            model_name="article",
            name="cover",
            field=swatmanga.common.fields.WebPVersatileImageField(
                upload_to=swatmanga.articles.models.article_cover_upload_to,
                validators=[
                    django.core.validators.FileExtensionValidator(
                        allowed_extensions=("jpeg", "jpg", "webp", "png", "gif")
                    )
                ],
                verbose_name="Cover Image",
            ),
        ),
    ]
