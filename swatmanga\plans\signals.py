# signals.py
from django.db.models.signals import post_save, post_delete
from django.dispatch import receiver
from django.core.cache import cache
from django_redis import get_redis_connection
from .models import Plan

@receiver([post_save, post_delete], sender=Plan)
def invalidate_plans_cache(sender, instance, **kwargs):
    redis_conn = get_redis_connection('default')
    
    # Invalidate list view cache (all versions)
    list_prefix = "GET:plans.list"
    keys = []
    keys+=redis_conn.keys(f"*views.decorators.cache.cache_page.{list_prefix}*")
    
    # Invalidate specific plan detail view
    retrieve_prefix = f"GET:plans.detail.{instance.pk}"
    keys+=redis_conn.keys(f"*views.decorators.cache.cache_page.{retrieve_prefix}*")
    
    # Also clear any filter-based cache
    keys+=redis_conn.keys("*plans*")
    
    if keys:
        redis_conn.delete(*keys)