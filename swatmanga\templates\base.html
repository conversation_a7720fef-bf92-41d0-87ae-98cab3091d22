{% load static i18n compress %}

<!DOCTYPE html>
{% get_current_language as LANGUAGE_CODE %}
<html lang="{{ LANGUAGE_CODE }}">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="x-ua-compatible" content="ie=edge" />
    <title>
      {% block title %}
        Swatmanga
      {% endblock title %}
    </title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="Your Ultimate Anime Manga Hub!" />
    <meta name="author" content="Beshr Alghalil" />
    <link rel="icon" href="{% static 'images/favicons/favicon.ico' %}" />
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;600;700&display=swap" rel="stylesheet" />

    {% block css %}
      <!-- Tailwind CSS -->
      <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet" />
      <!-- Project-specific CSS -->
      {% compress css %}
        <link href="{% static 'css/project.css' %}" rel="stylesheet" />
      {% endcompress %}
    {% endblock css %}

    {% block javascript %}
      <!-- Project-specific Javascript -->
      {% compress js %}
        <script defer src="{% static 'js/project.js' %}"></script>
      {% endcompress %}
      <!-- Inline script for navbar toggle -->
      <script>
        function toggleNavbar() {
          const menu = document.getElementById('navbar-menu');
          menu.classList.toggle('hidden');
        }
      </script>
    {% endblock javascript %}
  </head>
  <body class="{% block bodyclass %}{% endblock bodyclass %}">
    {% block body %}
      <div class="wrapper">

        <nav class="relative flex items-center justify-between px-4 h-[var(--header-height)] bg-[--header-footer-bg] shadow-lg">
          <a href="{% url 'home' %}" class="text-2xl font-bold" style="background: linear-gradient(45deg, var(--anime-primary), var(--anime-secondary)); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">
            Swatmanga
          </a>
          <button onclick="toggleNavbar()" class="md:hidden focus:outline-none">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <defs>
                <linearGradient id="grad" x1="0%" y1="0%" x2="100%" y2="0%">
                  <stop offset="0%" style="stop-color: var(--anime-primary);stop-opacity:1" />
                  <stop offset="100%" style="stop-color: var(--anime-secondary);stop-opacity:1" />
                </linearGradient>
              </defs>
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16m-7 6h7"></path>
            </svg>
          </button>
          <ul id="navbar-menu" class="hidden md:flex md:items-center md:space-x-6 absolute md:static top-full left-0 w-full md:w-auto bg-header-footer md:bg-transparent flex-col md:flex-row p-4 md:p-0 z-50 shadow-lg md:shadow-none">
            <li>
              <a href="{% url 'home' %}" class="block py-2 md:py-0 text-lg text-[var(--bs-gray-200)] md:text-[var(--bs-gray-300)] hover:text-[var(--bs-white)]">
                {% translate "Home" %}
              </a>
            </li>
            {% if request.user.is_authenticated %}
              <li>
                <a href="{% url 'users:profile' request.user.pk %}" class="block py-2 md:py-0 text-lg text-[var(--bs-gray-200)] md:text-[var(--bs-gray-300)] hover:text-[var(--bs-white)]">
                  {% translate "My Profile" %}
                </a>
              </li>
              <li>
                <a href="{% url 'account_logout' %}" class="block py-2 md:py-0 text-lg text-[var(--bs-gray-200)] md:text-[var(--bs-gray-300)] hover:text-[var(--bs-white)]">
                  {% translate "Sign Out" %}
                </a>
              </li>
            {% else %}
              <li>
                <a id="log-in-link" href="{% url 'account_login' %}" class="block py-2 md:py-0 text-lg text-[var(--bs-gray-200)] md:text-[var(--bs-gray-300)] hover:text-[var(--bs-white)]">
                  {% translate "Sign In" %}
                </a>
              </li>
              {% if ACCOUNT_ALLOW_REGISTRATION %}
                <li>
                  <a id="sign-up-link" href="{% url 'account_signup' %}" class="block py-2 md:py-0 text-lg text-[var(--bs-gray-200)] md:text-[var(--bs-gray-300)] hover:text-[var(--bs-white)]">
                    {% translate "Sign Up" %}
                  </a>
                </li>
              {% endif %}
            {% endif %}
          </ul>
        </nav>
        

        <div class="container flex flex-col items-center justify-center min-h-[calc(100vh-75px)] mt-4">
          {% if messages %}
            {% for message in messages %}
              <div class="alert relative p-4 my-2">
                {{ message }}
                <button onclick="this.parentElement.remove()" class="top-2 right-2 px-3 py-1 text-sm">
                  Close
                </button>
              </div>
            {% endfor %}
          {% endif %}

          {% block main %}
            {% block content %}
              <!-- Development Icon -->
              <div class="mb-6">
                <svg class="w-16 h-16" fill="url(#grad-icon)" stroke="none" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <defs>
                    <linearGradient id="grad-icon" x1="0%" y1="0%" x2="100%" y2="100%">
                      <stop offset="0%" style="stop-color: var(--anime-primary);stop-opacity:1" />
                      <stop offset="100%" style="stop-color: var(--anime-secondary);stop-opacity:1" />
                    </linearGradient>
                  </defs>
                  <!-- Construction icon (wrench and screwdriver) -->
                  <path d="M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z" />
                </svg>
              </div>
              <h2 class="mb-4">This Site is Under Development</h2>
              <p class="text-[var(--bs-gray-400)] mb-6">We're working hard to bring you a better experience. In the meantime, you can download our app or visit the old site.</p>
              <div class="flex flex-col sm:flex-row gap-4">
                <!-- Google Play Button -->
                <a href="https://play.google.com/store/apps/details?id=com.swat.apps.manga" target="_blank" class="custom-btn flex items-center justify-center gap-2">
                  <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path clip-rule="evenodd" d="M11.5763 5.5608C8.91373 4.12432 6.16498 2.64235 5.78592 2.44182C5.39709 2.21907 4.95701 2.14902 4.55109 2.21878C4.54332 2.22009 4.53555 2.22154 4.5278 2.2231C4.52046 2.22459 4.51315 2.22618 4.50588 2.22787C4.15987 2.30778 3.82822 2.50506 3.59054 2.83763C3.3557 3.16622 3.25 3.57343 3.25 4.01777V19.8414C3.25 20.2292 3.32574 20.6378 3.53817 20.9816C3.76563 21.3498 4.13866 21.617 4.62138 21.6616C4.67873 21.6669 4.73593 21.6656 4.79194 21.6579C5.1406 21.6593 5.47338 21.5469 5.78123 21.3794L5.89026 21.3203C6.02069 21.2496 6.23036 21.1359 6.50625 20.9867C8.39335 19.9665 13.3485 17.2875 16.9001 15.3566C16.9086 15.3519 16.917 15.3472 16.9253 15.3422C17.6023 14.9745 18.2278 14.6343 18.7706 14.339L19.6215 13.8763C19.9694 13.6872 20.2535 13.533 20.4594 13.4217C20.5624 13.366 20.645 13.3216 20.7058 13.2891L20.7786 13.2505L20.8047 13.2368L20.8225 13.2278C21.1442 13.0586 21.5878 12.699 21.716 12.1535C21.7879 11.8479 21.7491 11.5161 21.5674 11.2114C21.3966 10.9249 21.1295 10.7115 20.8257 10.5498L20.8185 10.5461C20.532 10.3976 18.9498 9.53864 16.9011 8.42061C16.8597 8.39802 16.8169 8.37968 16.7733 8.36551L15.2499 7.5432C14.2571 7.0073 12.9266 6.28927 11.5763 5.5608ZM13.7072 10.8508L8.31115 5.50399C9.11284 5.93622 9.99214 6.4105 10.8641 6.88094C12.2143 7.60934 13.5446 8.32732 14.5375 8.86319L15.29 9.26942L13.7072 10.8508ZM4.75 19.7825V4.087L12.6455 11.9105L4.75 19.7825ZM14.7758 11.9062L16.6828 10.0102C18.47 10.9849 19.8353 11.7257 20.1241 11.8757L20.1462 11.8878C20.1399 11.8916 20.1337 11.8952 20.1277 11.8984C19.9725 11.9777 19.1542 12.4228 17.9812 13.0608C17.5819 13.278 17.1414 13.5176 16.672 13.7727L14.7758 11.9062ZM8.40918 18.2524L13.7127 12.9646L15.2944 14.5219C13.0527 15.7393 10.4392 17.1542 8.40918 18.2524Z"/>
                  </svg>
                  Download on Google Play
                </a>
                <!-- Old Site Button -->
                <a href="/" class="custom-btn flex items-center justify-center gap-2">
                  <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9"/>
                  </svg>
                  Visit Old Site
                </a>
              </div>
            {% endblock content %}
          {% endblock main %}
        </div>
      </div>
    {% endblock body %}

    {% block modal %}
    {% endblock modal %}

    {% block inline_javascript %}
    {% endblock inline_javascript %}
  </body>
</html>