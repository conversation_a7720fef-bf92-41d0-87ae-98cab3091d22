from django.urls import path, include
from .api.auth import RegisterView, LoginView, LogoutView

urlpatterns = [
    # Allauth URLs (for password reset and social auth if needed)
    path('accounts/', include('allauth.urls')),
    
    # JWT Auth Endpoints
    path('api/auth/register/', RegisterView.as_view(), name='rest_register'),
    path('api/auth/login/', LoginView.as_view(), name='rest_login'),
    path('api/auth/logout/', LogoutView.as_view(), name='rest_logout'),
    
    # Simple JWT's built-in token endpoints
    path('api/token/', include('rest_framework_simplejwt.urls')),
]