from rest_framework import viewsets
from rest_framework.response import Response
from ..models import GoogleAdLink
from .serializers import GoogleAdsGroupedSchemaSerializer
from drf_spectacular.utils import extend_schema
from django.core.cache import cache


class GoogleAdsViewSet(viewsets.GenericViewSet):
    """
    A viewset that provides only read operations for GoogleAdLink.
    The list method returns grouped data by platform.
    """

    queryset = GoogleAdLink.objects.all()
    pagination_class = None

    @extend_schema(
        responses={200: GoogleAdsGroupedSchemaSerializer},
    )
    def list(self, request, *args, **kwargs):
        cache_key = "ads"
        cached_data = cache.get(cache_key)
        if cached_data:
            return Response(cached_data)

        queryset = self.get_queryset()
        data = {p[0]: [] for p in GoogleAdLink.PLATFORM_CHOICES}
        for ad in queryset:
            data[ad.platform].append(
                {
                    "type": ad.ad_type,
                    "key": ad.key,
                }
            )
        response = Response(data)
        cache.set(cache_key, response.data, 60 * 60 * 24)
        return response
