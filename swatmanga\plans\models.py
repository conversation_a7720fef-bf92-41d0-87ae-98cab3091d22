import os

from django.conf import settings
from django.core.validators import FileExtensionValidator
from django.db import models
from django.utils.text import slugify

from swatmanga.users.models import User
from django.utils import timezone
from dateutil.relativedelta import relativedelta


def plan_subscription_upload_to(instance, filename: str):
    image_filename = f"{instance.plan.id}.jpeg"
    if settings.POPULATING_FAKE_DATA:
        pdir = instance.user.username
    else:
        pdir = str(instance.user.id)
    return os.path.join("users", pdir, image_filename)


class Plan(models.Model):
    class PlanType(models.TextChoices):
        SUBSCRIPTION = "subscription", "Recurring Subscription"
        ONE_TIME = "one_time", "One-Time Purchase"

    title = models.CharField(max_length=255)
    slug = models.SlugField(max_length=255, auto_created=True)
    short_description = models.CharField(max_length=500)
    plan_type = models.CharField(
        max_length=20, choices=PlanType.choices, default=PlanType.SUBSCRIPTION
    )
    currency = models.CharField(max_length=3, help_text="Currency ISO 4217 Code")
    # TODO: consider rename to dissable_ads
    ads_enabled = models.BooleanField("Dissable ads", default=True, help_text="Subscribers won't see ads")
    duration_months = models.PositiveIntegerField(
        null=True, blank=True, help_text="Required for subscriptions"
    )
    add_coins = models.PositiveIntegerField(
        default=0,
        help_text="Coins to add immediately when purchased (one-time plans only)",
    )
    price = models.DecimalField(max_digits=4, decimal_places=2)
    params = models.JSONField(default=list, blank=True)
    color = models.CharField(max_length=10)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = "plans"

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(self.title)
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.title}"


class PlanFeature(models.Model):
    # make it plans instead of plan
    plan = models.ForeignKey(Plan, on_delete=models.DO_NOTHING, related_name="features")
    text = models.CharField(max_length=1000)
    slug = models.SlugField(auto_created=True, unique=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = "plans_features"

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(self.text)
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.plan} - {self.text}"


class PlanSubscription(models.Model):
    # TODO: signals should be used to notify managers,
    #  and users
    class PlanSubscriptionStatus(models.TextChoices):
        PENDING = "pending"
        SUBSCRIBED = "subscribed"
        CANCELED = "canceled"
        EXPIRED = "expired"

    user = models.ForeignKey(
        User, on_delete=models.CASCADE, related_name="plans_subscriptions"
    )
    plan = models.ForeignKey(
        Plan, on_delete=models.CASCADE, related_name="users_subscriptions"
    )
    user_discord = models.CharField(max_length=32, null=True, blank=True)
    payment_receipt = models.ImageField(
        upload_to=plan_subscription_upload_to,
        validators=[
            FileExtensionValidator(allowed_extensions=settings.ALLOWED_IMAGE_EXTENSIONS)
        ],
        null=True,
        blank=True,
    )
    status = models.CharField(
        max_length=20,
        choices=PlanSubscriptionStatus.choices,
        default=PlanSubscriptionStatus.PENDING,
    )
    params = models.JSONField(
        default=dict, blank=True, null=True, verbose_name="parameters"
    )
    expiration_date = models.DateTimeField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    @property
    def is_active(self) -> bool:
        if (
            self.status != PlanSubscription.PlanSubscriptionStatus.SUBSCRIBED
            or not self.expiration_date
        ):
            return False

        if self.plan.plan_type == self.plan.PlanType.SUBSCRIPTION:
            return self.expiration_date > timezone.now()

        return True

    @property
    def days_remaining(self) -> int | None:
        if (
            self.plan.plan_type != self.plan.PlanType.SUBSCRIPTION
            or not self.expiration_date
        ):
            return None
        delta = self.expiration_date - timezone.now()
        return delta.days if delta.days > 0 else 0

    def save(self, *args, **kwargs):
        is_new = not self.pk

        # Set expiration date for new subscriptions
        if (not self.expiration_date) and is_new and self.plan.plan_type == self.plan.PlanType.SUBSCRIPTION:
            if self.plan.duration_months:
                self.expiration_date = timezone.now() + relativedelta(
                    months=self.plan.duration_months
                )

        super().save(*args, **kwargs)

        # Add coins for new one-time purchases
        if (
            is_new
            and self.plan.plan_type == self.plan.PlanType.ONE_TIME
            and self.plan.add_coins > 0
        ):
            self.user.coins += self.plan.add_coins
            self.user.save()

    class Meta:
        db_table = "users_plans"

    def __str__(self):
        return f"{self.user} -> {self.plan} ({self.status})"


class PaymentMethod(models.Model):
    name = models.CharField(max_length=100, verbose_name="Payment Method")
    description = models.TextField(blank=True, null=True, verbose_name="Description")

    def __str__(self):
        return self.name


class PaymentDetail(models.Model):
    method = models.ForeignKey(
        PaymentMethod, related_name="details", on_delete=models.CASCADE
    )
    key = models.CharField(max_length=100, verbose_name="Detail Key")
    value = models.TextField(verbose_name="Detail Value")

    def __str__(self):
        return f"{self.key}: {self.value}"
