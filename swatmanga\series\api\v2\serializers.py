from ..v1.serializers import SerieDetailSerializer as SerieDetailSerializerV1
from rest_framework import serializers


class SerieDetailSerializer(SerieDetailSerializerV1):

    my_rating = serializers.SerializerMethodField()

    class Meta:
        model = SerieDetailSerializerV1.Meta.model
        fields = SerieDetailSerializerV1.Meta.fields + ["my_rating", "translator", "editor"]

    def get_my_rating(self, obj) -> str:
        return obj
