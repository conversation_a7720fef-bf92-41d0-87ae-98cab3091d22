python-slugify==8.0.4  # https://github.com/un33k/python-slugify
Pillow==10.4.0  # https://github.com/python-pillow/Pillow
rcssmin==1.1.2  # https://github.com/ndparker/rcssmin
argon2-cffi==23.1.0  # https://github.com/hynek/argon2_cffi
whitenoise==6.7.0  # https://github.com/evansd/whitenoise
redis==5.0.8  # https://github.com/redis/redis-py
hiredis==3.0.0  # https://github.com/redis/hiredis-py
python-magic==0.4.27
python-magic-bin==0.4.13; sys_platform == 'win32'
python-magic-bin==0.4.13; sys_platform == 'darwin'
user-agents==2.2.0
bcrypt==4.2.1
natsort==8.4.0
# Django
# ------------------------------------------------------------------------------
django==5.0.8  # pyup: < 5.1  # https://www.djangoproject.com/
django-environ==0.11.2  # https://github.com/joke2k/django-environ
django-model-utils==4.5.1  # https://github.com/jazzband/django-model-utils
django-allauth[mfa]==64.0.0  # https://github.com/pennersr/django-allauth
django-rest-auth==0.9.5
django-crispy-forms==2.3  # https://github.com/django-crispy-forms/django-crispy-forms
crispy-bootstrap5==2024.2  # https://github.com/django-crispy-forms/crispy-bootstrap5
django-compressor==4.5.1  # https://github.com/django-compressor/django-compressor
django-redis==5.4.0  # https://github.com/jazzband/django-redis
django-versatileimagefield==3.1  # https://github.com/respondcreate/django-versatileimagefield
django-computedfields==0.2.5 # https://github.com/netzkolchose/django-computedfields
django-filter==24.3
django-comments-xtd==2.10.0  # https://github.com/danirus/django-comments-xtd
django-cleanup==9.0.0  # https://pypi.org/project/django-cleanup/
# Django REST Framework
djangorestframework==3.15.2  # https://github.com/encode/django-rest-framework
django-cors-headers==4.4.0  # https://github.com/adamchainz/django-cors-headers
djangorestframework-simplejwt==5.3.1
django-admin-inline-paginator==0.4.0  # https://pypi.org/project/django-admin-inline-paginator
django-admin-autocomplete-filter==0.7.1
django-admin-sortable2==2.2.6  # https://django-admin-sortable2.readthedocs.io/en/latest/installation.html
# DRF-spectacular for api documentation
drf-spectacular==0.27.2  # https://github.com/tfranzel/drf-spectacular
django-hitcount==1.3.5  # https://pypi.org/project/django-hitcount/
drf-access-policy==1.5.0
django-jazzmin==3.0.1
# FCM
fcm-django==2.2.1
# Promethues
django-prometheus==2.3.1
# Rick text editor
django-tinymce==4.1.0
# Django tags
django-taggit==6.1.0
# Celery
django_celery_beat==2.7.0
# Logging
python-json-logger==3.3.0  # https://nhairs.github.io/python-json-logger/latest/quickstart/
python-logging-loki==0.3.1 