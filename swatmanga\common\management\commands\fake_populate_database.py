import os

import random
import shutil
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from django.conf import settings
from django.core.management.base import BaseCommand
from django.db import connection

from swatmanga.plans.factories import *
from swatmanga.series.factories import *


class Command(BaseCommand):
    help = "Populates the database with test data"

    def add_arguments(self, parser):
        parser.add_argument(
            "-f",
            "--factor",
            type=int,
            default=1,
            help="Scaling factor to control the amount of data generated",
        )

    def handle(self, *args, **kwargs):
        settings.POPULATING_FAKE_DATA = True
        scaling_factor = kwargs["factor"]
        self._drop_tables()
        self.stdout.write(
            self.style.SUCCESS(
                f"Starting data generation with scaling factor: {scaling_factor}"
            )
        )
        self.create_fake_data(factor=scaling_factor)
        self.stdout.write(self.style.SUCCESS("Database populated successfully"))

    def create_fake_data(self, factor=1):
        # Define batch sizes for different data types
        weights = {
            "genres": 10,
            "series": 50,
            "chapters": 10,
            "rating": 50,
            "donation": 5,
            "follow": 40,
            "fav": 50,
            "read": 100,
            "ch_image": 5,
            "users": 20,
            "plans": 10,
            "plan_features": 5,
            "plan_subscriptions": 20,
        }

        def batch_size(key):
            return weights.get(key, 1)

        users = self._generate_users(batch_size("users"))
        genres = self._generate_genres(batch_size("genres"))
        series_list = self._generate_series(batch_size("series"), genres)

        self._generate_chapters_and_images(series_list, batch_size)
        self._generate_interactions(batch_size, series_list)

        plans = self._generate_plans_and_features(
            batch_size("plans"), batch_size("plan_features")
        )
        self._generate_plan_subscriptions(
            batch_size("plan_subscriptions"), plans, users
        )

        self.stdout.write(self.style.SUCCESS(f"Data generation completed!"))

    def _generate_users(self, user_count):
        self.stdout.write(self.style.NOTICE(f"Generating {user_count} users..."))
        return UserFactory.create_batch(user_count)

    def _generate_genres(self, genre_count):
        self.stdout.write(self.style.NOTICE(f"Generating {genre_count} genres..."))
        return GenreFactory.create_batch(genre_count)

    def _generate_series(self, series_count, genres):
        self.stdout.write(self.style.NOTICE(f"Generating {series_count} series..."))
        return SerieFactory.create_batch(series_count, genres=genres)

    def _generate_chapters_and_images(self, series_list, batch_size):
        max_workers = min(10, os.cpu_count())
        self.stdout.write(
            self.style.NOTICE(f"Starting threaded generation of chapters and images...")
        )

        # Threaded function for generating chapters and images for a series
        def generate_for_series(serie):
            num_chapters = random.randint(1, batch_size("chapters"))
            self.stdout.write(
                self.style.NOTICE(
                    f"Series {serie.id}: Generating {num_chapters} chapters..."
                )
            )
            chapters = ChapterFactory.create_batch(num_chapters, serie=serie)

            for chapter in chapters:
                num_images = random.randint(1, batch_size("ch_image"))
                self.stdout.write(
                    self.style.NOTICE(
                        f"Chapter {chapter.id}: Generating {num_images} images..."
                    )
                )
                ChapterImageFactory.create_batch(num_images, chapter=chapter)

        # Parallelize chapter/image generation using threading
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            futures = [
                executor.submit(generate_for_series, serie) for serie in series_list
            ]
            for future in as_completed(futures):
                try:
                    future.result()
                except Exception as e:
                    self.stdout.write(
                        self.style.ERROR(f"Error generating chapters/images: {e}")
                    )

    def _generate_interactions(self, batch_size, series_list):
        self.stdout.write(
            self.style.NOTICE(
                f"Generating interactions (ratings, donations, follows, etc.)..."
            )
        )
        users = UserFactory.create_batch(batch_size("users"))

        # Create ratings
        RatingFactory.create_batch(batch_size("rating"), user=factory.Iterator(users))

        # Create donations
        DonationFactory.create_batch(
            batch_size("donation"), user=factory.Iterator(users)
        )

        # Create follows and favorites
        FollowFactory.create_batch(
            batch_size("follow"),
            user=factory.Iterator(users),
            serie=factory.Iterator(series_list),
        )
        FavoriteFactory.create_batch(
            batch_size("fav"),
            user=factory.Iterator(users),
            serie=factory.Iterator(series_list),
        )

        # Generate all chapters for interactions
        all_chapters = []
        for serie in series_list:
            chapters = ChapterFactory.create_batch(
                batch_size("chapters"), serie=serie
            )  # Save chapters directly
            all_chapters.extend(chapters)  # Add to the list of all chapters

        if not all_chapters:
            self.stdout.write(
                self.style.ERROR("No chapters found to generate read interactions.")
            )
            return

        # Create chapter reads
        ChapterReadFactory.create_batch(
            batch_size("read"),
            user=factory.Iterator(users),
            chapter=factory.Iterator(all_chapters),
        )

    def _generate_plans_and_features(self, plan_count, feature_count):
        self.stdout.write(
            self.style.NOTICE(
                f"Generating {plan_count} plans and {feature_count} features..."
            )
        )
        plans = PlanFactory.create_batch(plan_count)

        for plan in plans:
            PlanFeatureFactory.create_batch(feature_count, plan=plan)
        return plans

    def _generate_plan_subscriptions(self, subscription_count, plans, users):
        self.stdout.write(
            self.style.NOTICE(f"Generating {subscription_count} plan subscriptions...")
        )
        PlanSubscriptionFactory.create_batch(
            subscription_count,
            plan=factory.Iterator(plans),
            user=factory.Iterator(users),
        )

    def _drop_tables(self):
        if not settings.DEBUG:
            self.stdout.write(
                self.style.ERROR("This command is only allowed in DEBUG mode.")
            )
            return

        self.stdout.write(
            self.style.WARNING("Dropping all data and resetting tables...")
        )
        shutil.rmtree(settings.MEDIA_ROOT)

        with connection.cursor() as cursor:
            cursor.execute(
                """
                SELECT tablename FROM pg_tables
                WHERE schemaname = 'public'
                AND tablename NOT LIKE 'django_%'
                AND tablename NOT LIKE 'auth_%'
            """
            )
            tables = cursor.fetchall()

            for table in tables:
                self.stdout.write(
                    self.style.WARNING(f"Deleting data from {table[0]}...")
                )
                cursor.execute(f"TRUNCATE TABLE {table[0]} RESTART IDENTITY CASCADE")

        self.stdout.write(self.style.SUCCESS("All tables cleared successfully!"))
