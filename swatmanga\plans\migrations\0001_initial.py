# Generated by Django 5.0.8 on 2024-08-15 13:48

import django.core.validators
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models

import swatmanga.plans.models


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="Plan",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("slug", models.SlugField(auto_created=True, max_length=255)),
                ("title", models.CharField(max_length=255)),
                ("short_description", models.Char<PERSON>ield(max_length=500)),
                (
                    "currency",
                    models.CharField(help_text="Currency ISO 4217 Code", max_length=3),
                ),
                ("price", models.DecimalField(decimal_places=2, max_digits=4)),
                ("color", models.<PERSON>r<PERSON><PERSON>(max_length=10)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "db_table": "plans",
            },
        ),
        migrations.CreateModel(
            name="PlanFeature",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("slug", models.SlugField(auto_created=True, unique=True)),
                ("text", models.CharField()),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "plan",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="features",
                        to="plans.plan",
                    ),
                ),
            ],
            options={
                "db_table": "plans_features",
            },
        ),
        migrations.CreateModel(
            name="PlanSubscription",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "user_discord",
                    models.CharField(blank=True, max_length=32, null=True),
                ),
                (
                    "payment_receipt",
                    models.ImageField(
                        upload_to=swatmanga.plans.models.plan_subscription_upload_to,
                        validators=[
                            django.core.validators.FileExtensionValidator(
                                allowed_extensions=[
                                    ".jpeg",
                                    ".jpg",
                                    ".webp",
                                    ".png",
                                    ".gif",
                                ]
                            )
                        ],
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[(1, "pending"), (2, "subscribed"), (3, "canceled")],
                        default=1,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "plan",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="users_subscriptions",
                        to="plans.plan",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="plans_subscriptions",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "db_table": "users_plans",
            },
        ),
        migrations.CreateModel(
            name="UserCoins",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("coins", models.IntegerField()),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "user",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="coins",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "db_table": "users_coins",
            },
        ),
    ]
