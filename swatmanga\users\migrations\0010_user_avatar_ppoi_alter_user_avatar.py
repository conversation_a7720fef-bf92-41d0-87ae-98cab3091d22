# Generated by Django 5.0.8 on 2025-04-04 08:38

import django.core.validators
import swatmanga.common.fields
import swatmanga.users.models
import versatileimagefield.fields
from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ("users", "0009_merge_0007_alter_user_coins_0008_alter_user_coins"),
    ]

    operations = [
        migrations.AddField(
            model_name="user",
            name="avatar_ppoi",
            field=versatileimagefield.fields.PPOIField(
                default="0.5x0.5", editable=False, max_length=20
            ),
        ),
        migrations.AlterField(
            model_name="user",
            name="avatar",
            field=swatmanga.common.fields.WebPVersatileImageField(
                blank=True,
                null=True,
                upload_to=swatmanga.users.models.user_avatar_upload_to,
                validators=[
                    django.core.validators.FileExtensionValidator(
                        allowed_extensions=("jpeg", "jpg", "webp", "png", "gif")
                    )
                ],
                verbose_name="Avatar Image",
            ),
        ),
    ]
