import os

import argparse
import psycopg2
import shutil
from concurrent.futures import ThreadPoolExecutor, as_completed


def copy_image(row, image_path, output_dir):
    try:
        poster_path = row[0]  # The value in the image_column
        # Construct the full output path
        output_path = os.path.join(output_dir, poster_path)

        # Ensure the directories exist
        os.makedirs(os.path.dirname(output_path), exist_ok=True)

        # Copy the image
        shutil.copy(image_path, output_path)
        print(f"Copied image to {output_path}")
    except Exception as e:
        print(f"Error copying image: {e}")


def copy_images(dburl, table, image_column, image_path, output_dir, pool_size):
    try:
        # Connect to PostgreSQL
        conn = psycopg2.connect(dburl)
        cursor = conn.cursor()

        # Fetch rows from the specified table
        cursor.execute(
            f"SELECT {image_column} FROM {table} WHERE {image_column} IS NOT NULL"
        )
        rows = cursor.fetchall()

        # Create a thread pool with a limit of 10 concurrent copying operations
        with ThreadPoolExecutor(max_workers=pool_size) as executor:
            futures = [
                executor.submit(copy_image, row, image_path, output_dir) for row in rows
            ]

            # Wait for all the futures to complete
            for future in as_completed(futures):
                future.result()  # Get the result (or raise an exception if occurred)

        cursor.close()
        conn.close()
        print("Image copying completed.")

    except (Exception, psycopg2.DatabaseError) as error:
        print(f"Error: {error}")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Copy images for series")
    parser.add_argument("-d", "--dburl", required=True, help="PostgreSQL database url")
    parser.add_argument("-t", "--table", required=True, help="Table name")
    parser.add_argument(
        "-c",
        "--image-column",
        required=True,
        help="Column that contains the image path",
    )
    parser.add_argument(
        "-i",
        "--image-path",
        required=True,
        help="Path of the source image file (e.g., sample_poster.webp)",
    )
    parser.add_argument(
        "-o",
        "--output-dir",
        required=True,
        help="Directory where the images will be saved",
    )
    parser.add_argument(
        "-p",
        "--pool-size",
        required=False,
        help="Max thread pool size",
        default=20,
        type=int,
    )

    args = parser.parse_args()

    copy_images(
        args.dburl,
        args.table,
        args.image_column,
        args.image_path,
        args.output_dir,
        args.pool_size,
    )
