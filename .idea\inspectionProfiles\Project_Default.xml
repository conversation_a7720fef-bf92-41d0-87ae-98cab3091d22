<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="108">
            <item index="0" class="java.lang.String" itemvalue="blinker" />
            <item index="1" class="java.lang.String" itemvalue="Werkzeug" />
            <item index="2" class="java.lang.String" itemvalue="amqp" />
            <item index="3" class="java.lang.String" itemvalue="cryptography" />
            <item index="4" class="java.lang.String" itemvalue="setuptools" />
            <item index="5" class="java.lang.String" itemvalue="MarkupSafe" />
            <item index="6" class="java.lang.String" itemvalue="Jinja2" />
            <item index="7" class="java.lang.String" itemvalue="redis" />
            <item index="8" class="java.lang.String" itemvalue="Faker" />
            <item index="9" class="java.lang.String" itemvalue="phonenumberslite" />
            <item index="10" class="java.lang.String" itemvalue="grpcio-status" />
            <item index="11" class="java.lang.String" itemvalue="google-api-python-client" />
            <item index="12" class="java.lang.String" itemvalue="grpcio" />
            <item index="13" class="java.lang.String" itemvalue="itsdangerous" />
            <item index="14" class="java.lang.String" itemvalue="google-cloud-firestore" />
            <item index="15" class="java.lang.String" itemvalue="Flask" />
            <item index="16" class="java.lang.String" itemvalue="google-auth" />
            <item index="17" class="java.lang.String" itemvalue="pytest" />
            <item index="18" class="java.lang.String" itemvalue="pytest-django" />
            <item index="19" class="java.lang.String" itemvalue="wheel" />
            <item index="20" class="java.lang.String" itemvalue="twine" />
            <item index="21" class="java.lang.String" itemvalue="psycopg2-binary" />
            <item index="22" class="java.lang.String" itemvalue="pylinkvalidator" />
            <item index="23" class="java.lang.String" itemvalue="mkdocs" />
            <item index="24" class="java.lang.String" itemvalue="importlib-metadata" />
            <item index="25" class="java.lang.String" itemvalue="coreschema" />
            <item index="26" class="java.lang.String" itemvalue="attrs" />
            <item index="27" class="java.lang.String" itemvalue="pyyaml" />
            <item index="28" class="java.lang.String" itemvalue="coreapi" />
            <item index="29" class="java.lang.String" itemvalue="django-filter" />
            <item index="30" class="java.lang.String" itemvalue="django-guardian" />
            <item index="31" class="java.lang.String" itemvalue="pytest-cov" />
            <item index="32" class="java.lang.String" itemvalue="markdown" />
            <item index="33" class="java.lang.String" itemvalue="transifex-client" />
            <item index="34" class="java.lang.String" itemvalue="inflection" />
            <item index="35" class="java.lang.String" itemvalue="pygments" />
            <item index="36" class="java.lang.String" itemvalue="Babel" />
            <item index="37" class="java.lang.String" itemvalue="docutils" />
            <item index="38" class="java.lang.String" itemvalue="requests" />
            <item index="39" class="java.lang.String" itemvalue="urllib3" />
            <item index="40" class="java.lang.String" itemvalue="idna" />
            <item index="41" class="java.lang.String" itemvalue="watchdog" />
            <item index="42" class="java.lang.String" itemvalue="decorator" />
            <item index="43" class="java.lang.String" itemvalue="pydot" />
            <item index="44" class="java.lang.String" itemvalue="qrcode" />
            <item index="45" class="java.lang.String" itemvalue="python-dateutil" />
            <item index="46" class="java.lang.String" itemvalue="pyopenssl" />
            <item index="47" class="java.lang.String" itemvalue="XlsxWriter" />
            <item index="48" class="java.lang.String" itemvalue="freezegun" />
            <item index="49" class="java.lang.String" itemvalue="num2words" />
            <item index="50" class="java.lang.String" itemvalue="ebaysdk" />
            <item index="51" class="java.lang.String" itemvalue="libsass" />
            <item index="52" class="java.lang.String" itemvalue="passlib" />
            <item index="53" class="java.lang.String" itemvalue="xlrd" />
            <item index="54" class="java.lang.String" itemvalue="python-stdnum" />
            <item index="55" class="java.lang.String" itemvalue="rjsmin" />
            <item index="56" class="java.lang.String" itemvalue="ofxparse" />
            <item index="57" class="java.lang.String" itemvalue="xlwt" />
            <item index="58" class="java.lang.String" itemvalue="pyserial" />
            <item index="59" class="java.lang.String" itemvalue="vobject" />
            <item index="60" class="java.lang.String" itemvalue="zeep" />
            <item index="61" class="java.lang.String" itemvalue="chardet" />
            <item index="62" class="java.lang.String" itemvalue="polib" />
            <item index="63" class="java.lang.String" itemvalue="pyusb" />
            <item index="64" class="java.lang.String" itemvalue="pytz" />
            <item index="65" class="java.lang.String" itemvalue="geoip2" />
            <item index="66" class="java.lang.String" itemvalue="drf-access-policy" />
            <item index="67" class="java.lang.String" itemvalue="django-environ" />
            <item index="68" class="java.lang.String" itemvalue="sphinx-autobuild" />
            <item index="69" class="java.lang.String" itemvalue="factory-boy" />
            <item index="70" class="java.lang.String" itemvalue="crispy-bootstrap5" />
            <item index="71" class="java.lang.String" itemvalue="mypy" />
            <item index="72" class="java.lang.String" itemvalue="django-stubs" />
            <item index="73" class="java.lang.String" itemvalue="pre-commit" />
            <item index="74" class="java.lang.String" itemvalue="python-slugify" />
            <item index="75" class="java.lang.String" itemvalue="djangorestframework-stubs" />
            <item index="76" class="java.lang.String" itemvalue="django-extensions" />
            <item index="77" class="java.lang.String" itemvalue="drf-spectacular" />
            <item index="78" class="java.lang.String" itemvalue="djangorestframework-simplejwt" />
            <item index="79" class="java.lang.String" itemvalue="ipdb" />
            <item index="80" class="java.lang.String" itemvalue="django-model-utils" />
            <item index="81" class="java.lang.String" itemvalue="djangorestframework" />
            <item index="82" class="java.lang.String" itemvalue="django-cors-headers" />
            <item index="83" class="java.lang.String" itemvalue="django-versatileimagefield" />
            <item index="84" class="java.lang.String" itemvalue="coverage" />
            <item index="85" class="java.lang.String" itemvalue="rcssmin" />
            <item index="86" class="java.lang.String" itemvalue="django-comments-xtd" />
            <item index="87" class="java.lang.String" itemvalue="django-anymail" />
            <item index="88" class="java.lang.String" itemvalue="pytest-sugar" />
            <item index="89" class="java.lang.String" itemvalue="django-hitcount" />
            <item index="90" class="java.lang.String" itemvalue="djlint" />
            <item index="91" class="java.lang.String" itemvalue="django-coverage-plugin" />
            <item index="92" class="java.lang.String" itemvalue="django-crispy-forms" />
            <item index="93" class="java.lang.String" itemvalue="whitenoise" />
            <item index="94" class="java.lang.String" itemvalue="django-compressor" />
            <item index="95" class="java.lang.String" itemvalue="gunicorn" />
            <item index="96" class="java.lang.String" itemvalue="django-debug-toolbar" />
            <item index="97" class="java.lang.String" itemvalue="hiredis" />
            <item index="98" class="java.lang.String" itemvalue="sphinx" />
            <item index="99" class="java.lang.String" itemvalue="django" />
            <item index="100" class="java.lang.String" itemvalue="django-computedfields" />
            <item index="101" class="java.lang.String" itemvalue="psycopg" />
            <item index="102" class="java.lang.String" itemvalue="ruff" />
            <item index="103" class="java.lang.String" itemvalue="django-redis" />
            <item index="104" class="java.lang.String" itemvalue="argon2-cffi" />
            <item index="105" class="java.lang.String" itemvalue="django-allauth" />
            <item index="106" class="java.lang.String" itemvalue="Pillow" />
            <item index="107" class="java.lang.String" itemvalue="django_silk" />
          </list>
        </value>
      </option>
    </inspection_tool>
    <inspection_tool class="PyPep8NamingInspection" enabled="true" level="WEAK WARNING" enabled_by_default="true">
      <option name="ignoredErrors">
        <list>
          <option value="N806" />
        </list>
      </option>
    </inspection_tool>
    <inspection_tool class="PyShadowingBuiltinsInspection" enabled="true" level="WEAK WARNING" enabled_by_default="true">
      <option name="ignoredNames">
        <list>
          <option value="format" />
        </list>
      </option>
    </inspection_tool>
    <inspection_tool class="PyUnresolvedReferencesInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredIdentifiers">
        <list>
          <option value="djodoorestframework.serializers.ModelSerializer.Meta" />
        </list>
      </option>
    </inspection_tool>
  </profile>
</component>