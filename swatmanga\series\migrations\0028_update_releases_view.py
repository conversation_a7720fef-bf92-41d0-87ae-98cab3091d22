# Generated by Django 5.0.8 on 2024-11-01 13:20

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ("series", "0027_auto_20241101_1009"),
    ]

    operations = [
        migrations.RunSQL(
            """
                DROP MATERIALIZED VIEW IF EXISTS releases;
                CREATE MATERIALIZED VIEW releases AS
                WITH ranked_series AS (
                    SELECT
                        s.id AS serie_id,
                        s.title,
                        s.latest_chapter_updated_at,
                        s.slug,
                        jsonb_build_object('id', st.id, 'name', st.name) AS type,
                        jsonb_build_object('id', ss.id, 'name', ss.name) AS status,
                        jsonb_agg(jsonb_build_object('id', g.id, 'name', g.name)) AS genres,
                        s.poster,
                        s.is_hot,
                        s.views_count,
                        s.rating
                    FROM
                        series s
                    LEFT JOIN serie_types st ON s.type_id = st.id
                    LEFT JOIN serie_statuses ss ON s.status_id = ss.id
                    LEFT JOIN series_genres sg ON s.id = sg.serie_id
                    LEFT JOIN genres g ON sg.genre_id = g.id
                    WHERE
                        s.latest_chapter_updated_at IS NOT NULL
                    GROUP BY
                        s.id, st.id, ss.id
                    ORDER BY
                        s.latest_chapter_updated_at DESC
                    LIMIT 200
                )
                SELECT
                    rs.serie_id,
                    rs.title,
                    rs.latest_chapter_updated_at,
                    rs.slug,
                    rs.type,
                    rs.status,
                    rs.genres,
                    rs.poster,
                    rs.is_hot,
                    rs.views_count,
                    rs.rating,
                    jsonb_agg(
                        jsonb_build_object(
                            'id', c.id,
                            'title', c.title,
                            'chapter', c.chapter
                        ) ORDER BY c.created_at DESC
                    ) AS chapters
                FROM
                    ranked_series rs
                LEFT JOIN LATERAL (
                    SELECT
                        c.id,
                        c.title,
                        c.chapter,
                        c.created_at
                    FROM
                        chapters c
                    WHERE
                        c.serie_id = rs.serie_id
                    ORDER BY
                        c.created_at DESC
                    LIMIT 4
                ) c ON true
                GROUP BY
                    rs.serie_id,
                    rs.title,
                    rs.latest_chapter_updated_at,
                    rs.slug,
                    rs.type,
                    rs.status,
                    rs.genres,
                    rs.poster,
                    rs.is_hot,
                    rs.views_count,
                    rs.rating;

                """,
            reverse_sql="DROP MATERIALIZED VIEW IF EXISTS releases;",
        ),
        migrations.RunSQL(
            "CREATE UNIQUE INDEX releases_serie_id_idx ON releases (serie_id);"
        ),
        migrations.RunSQL("REFRESH MATERIALIZED VIEW CONCURRENTLY releases;"),
    ]
