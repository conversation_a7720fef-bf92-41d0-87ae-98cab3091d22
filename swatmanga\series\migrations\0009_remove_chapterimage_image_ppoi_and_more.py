# Generated by Django 5.0.8 on 2024-09-08 13:22

import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("series", "0008_chapter_allow_comments_serie_allow_comments"),
    ]

    operations = [
        migrations.RemoveField(
            model_name="chapterimage",
            name="image_ppoi",
        ),
        migrations.RemoveField(
            model_name="serie",
            name="cover_ppoi",
        ),
        migrations.AddField(
            model_name="chapterread",
            name="created_at",
            field=models.DateTimeField(
                auto_now_add=True, default=django.utils.timezone.now
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="chapterview",
            name="created_at",
            field=models.DateTimeField(
                auto_now_add=True, default=django.utils.timezone.now
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="serieview",
            name="created_at",
            field=models.DateTimeField(
                auto_now_add=True, default=django.utils.timezone.now
            ),
            preserve_default=False,
        ),
    ]
