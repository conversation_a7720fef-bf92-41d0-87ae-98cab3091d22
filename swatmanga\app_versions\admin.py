from django.contrib import admin
from .models import AppVersions

@admin.register(AppVersions)
class AppVersionsAdmin(admin.ModelAdmin):
    list_display = ("latest_version", "minimum_supported_version", "updated_at")
    readonly_fields = ("updated_at",)  # Prevent manual editing of timestamps

    def has_add_permission(self, request):
        """Prevent adding new records manually."""
        return False if AppVersions.objects.exists() else True

    def has_delete_permission(self, request, obj=None):
        """Prevent deleting the singleton instance."""
        return False
