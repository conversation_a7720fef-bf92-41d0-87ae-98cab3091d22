# ruff: noqa
from django.conf import settings
from django.conf.urls.static import static
from django.contrib import admin
from django.urls import include, path
from django.views import defaults as default_views
from django.views.generic import TemplateView
from drf_spectacular.views import SpectacularAPIView, SpectacularSwaggerView
from rest_framework_simplejwt.views import TokenObtainPairView, TokenRefreshView

from swatmanga.common.api.prometheus import secure_metrics

from django.http import JsonResponse
from django.utils import timezone

def healthz(request):
    return JsonResponse({"status": "ok", "timestamp": timezone.now()})

urlpatterns = [
    path("", TemplateView.as_view(template_name="pages/home.html"), name="home"),
    path("healthz/", healthz, name="health"),
    path(
        "about/",
        TemplateView.as_view(template_name="pages/about.html"),
        name="about",
    ),
    # Django Admin, use {% url 'admin:index' %}
    path(settings.ADMIN_URL, admin.site.urls),
    # User management
    path("users/", include("swatmanga.users.urls", namespace="users")),
    path("accounts/", include("allauth.urls")),
    # custom urls includes go here
    path("series/", include("swatmanga.series.urls", namespace="series")),
    path("comments/", include("swatmanga.comments.urls", namespace="comments")),
    # Media files
    *static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT),
]

# API URLS
urlpatterns += [
    # API base url
    path("api/", include("config.api_urls", namespace="api")),

    path(
        "api/docs/",
        SpectacularSwaggerView.as_view(url_name="api-docs"),
        name="api-docs",
    ),
]

urlpatterns += [
    # path('api/v1/accounts/register', RegisterView.as_view(), name='rest_register'),
    # path('api/v1/accounts/verify-email/$', VerifyEmailView.as_view(), name='rest_verify_email'),
    path('metrics', secure_metrics, name='metrics'),
]

if settings.DEBUG:
    # This allows the error pages to be debugged during development, just visit
    # these url in browser to see how these error pages look like.
    urlpatterns += [
        path(
            "400/",
            default_views.bad_request,
            kwargs={"exception": Exception("Bad Request!")},
        ),
        path(
            "403/",
            default_views.permission_denied,
            kwargs={"exception": Exception("Permission Denied")},
        ),
        path(
            "404/",
            default_views.page_not_found,
            kwargs={"exception": Exception("Page not Found")},
        ),
        path("500/", default_views.server_error),
    ]
    if "debug_toolbar" in settings.INSTALLED_APPS:
        import debug_toolbar

        urlpatterns += [path("__debug__/", include(debug_toolbar.urls))]
    if "silk" in settings.INSTALLED_APPS:
        import silk

        urlpatterns += [path("silk/", include("silk.urls", namespace="silk"))]
