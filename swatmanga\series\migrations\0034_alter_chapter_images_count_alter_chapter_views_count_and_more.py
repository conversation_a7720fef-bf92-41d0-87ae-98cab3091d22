# Generated by Django 5.0.8 on 2025-02-22 17:29

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("series", "0033_alter_chapterread_created_at"),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name="chapter",
            name="images_count",
            field=models.IntegerField(db_default=0, editable=False),
        ),
        migrations.Alter<PERSON>ield(
            model_name="chapter",
            name="views_count",
            field=models.IntegerField(db_default=0, editable=False),
        ),
        migrations.AlterField(
            model_name="genre",
            name="series_count",
            field=models.IntegerField(db_default=0, default=0, editable=False),
        ),
        migrations.AlterField(
            model_name="serie",
            name="chapters_count",
            field=models.IntegerField(db_default=0, default=0, editable=False),
        ),
        migrations.AlterField(
            model_name="serie",
            name="donations_count",
            field=models.IntegerField(db_default=0, default=0, editable=False),
        ),
        migrations.Alter<PERSON>ield(
            model_name="serie",
            name="favorites_count",
            field=models.IntegerField(db_default=0, default=0, editable=False),
        ),
        migrations.AlterField(
            model_name="serie",
            name="followers_count",
            field=models.IntegerField(db_default=0, default=0, editable=False),
        ),
        migrations.AlterField(
            model_name="serie",
            name="rating",
            field=models.DecimalField(
                db_default=0.0,
                decimal_places=1,
                default=0.0,
                editable=False,
                max_digits=3,
            ),
        ),
        migrations.AlterField(
            model_name="serie",
            name="ratings_count",
            field=models.IntegerField(db_default=0, default=0, editable=False),
        ),
        migrations.AlterField(
            model_name="serie",
            name="views_count",
            field=models.IntegerField(db_default=0, default=0, editable=False),
        ),
    ]
