from django.contrib.contenttypes.models import ContentType
from django_comments.models import CommentFlag
from django_comments_xtd.api.serializers import (
    ReadCommentSerializer as XtdReadCommentSerializer,
)
from django_comments_xtd.api.serializers import (
    WriteCommentSerializer as XtdWriteCommentSerializer,
)
from django_comments_xtd.models import DISLIKEDIT_FLAG, LIKEDIT_FLAG, XtdComment
from django_comments_xtd.utils import get_app_model_options
from rest_framework import serializers

from swatmanga.series.api.v1.utils import humanize_time_difference
from swatmanga.series.models import Chapter, Serie
from swatmanga.articles.models import Article
from swatmanga.users.api.serializers import UserSerializer


CONTENT_TYPE_CHOICES = [
    (i._meta.label_lower, f"{i._meta.app_label} | {i._meta.model_name}")
    for i in (Chapter, Serie, Article)
]


class WriteCommentSerializer(XtdWriteCommentSerializer):
    id = serializers.IntegerField(read_only=True)
    name = serializers.CharField(allow_blank=True, required=False, default="")
    email = serializers.EmailField(allow_blank=True, required=False, default="")
    content_type = serializers.ChoiceField(choices=CONTENT_TYPE_CHOICES)

    def validate_content_type(self, value):
        return value
    
    def validate(self, data):
        data = super().validate(data)
        
        target = self.form.target_object
        
        if hasattr(target, 'allow_comments') and not target.allow_comments:
            raise serializers.ValidationError(
                {"detail": "Comments are not allowed on this object."}
            )
        return data


class WriteCommentSerializerSchema(serializers.Serializer):
    id = serializers.IntegerField(read_only=True)
    content_type = serializers.ChoiceField(choices=CONTENT_TYPE_CHOICES)
    object_pk = serializers.IntegerField()
    comment = serializers.CharField()


class CommentReplySerializer(serializers.Serializer):
    comment = serializers.CharField()


class CommentsCountSerializer(serializers.Serializer):
    count = serializers.IntegerField()


class ReadFlagFieldSchema(serializers.Serializer):
    flag_map = {
        LIKEDIT_FLAG: "like",
        DISLIKEDIT_FLAG: "dislike",
        CommentFlag.SUGGEST_REMOVAL: CommentFlag.SUGGEST_REMOVAL,
    }

    id = serializers.IntegerField(read_only=True)
    flag = serializers.ChoiceField(
        choices=("removal", "like", "dislike"), read_only=True
    )
    user = UserSerializer(read_only=True)

    def to_representation(self, instance):
        data = super().to_representation(instance)
        data["flag"] = self.flag_map[data["flag"]]


class ReadCommentSerializer(XtdReadCommentSerializer):
    FLAG_MAP = {
        LIKEDIT_FLAG: "like",
        DISLIKEDIT_FLAG: "dislike",
        CommentFlag.SUGGEST_REMOVAL: "report",
    }
    user = UserSerializer(read_only=True)
    # flags = ReadFlagFieldSchema(many=True)
    submit_date = serializers.DateTimeField()
    submit_date_humanized = serializers.SerializerMethodField()
    likes_count = serializers.IntegerField(read_only=True)
    dislikes_count = serializers.IntegerField(read_only=True)
    my_feedback = serializers.SerializerMethodField()

    class Meta:
        model = XtdComment
        fields = (
            "id",
            "user",
            "comment",
            "submit_date",
            "submit_date_humanized",
            "parent_id",
            "level",
            "is_removed",
            "allow_reply",
            "nested_count",
            "likes_count",
            "dislikes_count",
            "my_feedback",
            # "flags",
        )
        
    def get_submit_date_humanized(self, obj):
        return humanize_time_difference(obj.submit_date, locale="ar")


    def get_my_feedback(self, obj) -> str | None:
        user = self.request.user
        if not user or not user.is_authenticated:
            return None

        try:
            feedback = CommentFlag.objects.get(user=user, comment=obj)
        except CommentFlag.DoesNotExist:
            feedback = None
        else:
            feedback = self.FLAG_MAP[feedback.flag]
        return feedback


class ReadCommentSerializerSchema(serializers.ModelSerializer):
    user = UserSerializer(read_only=True)
    submit_date = serializers.DateTimeField(read_only=True)
    submit_date_humanized = serializers.CharField(read_only=True)
    parent_id = serializers.IntegerField(default=0, read_only=True)
    level = serializers.IntegerField(read_only=True)
    is_removed = serializers.BooleanField(read_only=True)
    comment = serializers.CharField(read_only=True)
    allow_reply = serializers.BooleanField(read_only=True)
    # flags = ReadFlagFieldSchema(many=True, read_only=True)
    likes_count = serializers.IntegerField(read_only=True)
    dislikes_count = serializers.IntegerField(read_only=True)
    my_feedback = serializers.ChoiceField(
        choices=("like", "dislike", "report"), allow_null=True, read_only=True
    )

    class Meta:
        model = XtdComment
        fields = (
            "id",
            "user",
            "comment",
            "submit_date",
            "submit_date_humanized",
            "parent_id",
            "level",
            "is_removed",
            "allow_reply",
            "nested_count",
            "likes_count",
            "dislikes_count",
            "my_feedback",
            # "flags",
        )


class FlagSerializer(serializers.ModelSerializer):
    flag_choices = {
        "like": LIKEDIT_FLAG,
        "dislike": DISLIKEDIT_FLAG,
        "report": CommentFlag.SUGGEST_REMOVAL,
    }

    flag = serializers.ChoiceField(choices=("like", "dislike", "report"))

    class Meta:
        model = CommentFlag
        fields = (
            "comment",
            "flag",
        )

    def validate(self, data):
        # Check commenting options on object being commented.
        option = ""
        if data["flag"] in ["like", "dislike"]:
            option = "allow_feedback"
        elif data["flag"] == "report":
            option = "allow_flagging"
        comment = data["comment"]
        ctype = ContentType.objects.get_for_model(comment.content_object)
        key = "%s.%s" % (ctype.app_label, ctype.model)
        if not get_app_model_options(content_type=key)[option]:
            raise serializers.ValidationError(
                "Comments posted to instances of '%s' are not explicitly "
                "allowed to receive '%s' flags. Check the "
                "COMMENTS_XTD_APP_MODEL_OPTIONS setting." % (key, data["flag"])
            )
        data["flag"] = self.flag_choices[data["flag"]]
        return data


class FlagSerializerSchema(FlagSerializer):
    class Meta:
        model = CommentFlag
        fields = ["flag"]
