import typing
from allauth.headless.tokens.base import AbstractTokenStrategy
from django.http import HttpRequest
from rest_framework_simplejwt.tokens import RefreshToken


class JwtTokenStrategy(AbstractTokenStrategy):
    def create_access_token_payload(self, request: HttpRequest):
        refresh = self.create_access_token(request)
        return {
            "refresh": str(refresh),
            "access": str(refresh.access_token),
        }

    def create_access_token(self, request: HttpRequest):
        return RefreshToken.for_user(request.user)
