# Generated by Django 5.0.8 on 2025-05-30 05:50

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("series", "0042_auto_20250530_0549"),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name="serie",
            name="genres",
            field=models.ManyToManyField(related_name="series", to="series.genre"),
        ),
        migrations.AlterField(
            model_name="serie",
            name="status",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.DO_NOTHING,
                related_name="series",
                to="series.seriestatus",
            ),
        ),
        migrations.AlterField(
            model_name="serie",
            name="type",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.DO_NOTHING,
                related_name="series",
                to="series.serietype",
            ),
        ),
    ]
