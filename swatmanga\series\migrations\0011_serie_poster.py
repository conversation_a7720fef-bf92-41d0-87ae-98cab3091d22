# Generated by Django 5.0.8 on 2024-09-09 11:54

import django.core.validators
from django.db import migrations

import swatmanga.common.fields
import swatmanga.series.models


class Migration(migrations.Migration):
    dependencies = [
        ("series", "0010_remove_serieview_serie_remove_serieview_user_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="serie",
            name="poster",
            field=swatmanga.common.fields.WebPVersatileImageField(
                blank=True,
                null=True,
                upload_to=swatmanga.series.models.serie_poster_upload_to,
                validators=[
                    django.core.validators.FileExtensionValidator(
                        allowed_extensions=["jpeg", "jpg", "webp", "png", "gif"]
                    )
                ],
                verbose_name="Poster Image",
            ),
        ),
    ]
