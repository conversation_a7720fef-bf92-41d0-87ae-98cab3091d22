# Generated by Django 5.0.8 on 2024-09-09 07:35

import django.core.validators
from django.db import migrations

import swatmanga.common.fields
import swatmanga.series.models


class Migration(migrations.Migration):
    dependencies = [
        ("series", "0009_remove_chapterimage_image_ppoi_and_more"),
    ]

    operations = [
        migrations.RemoveField(
            model_name="serieview",
            name="serie",
        ),
        migrations.RemoveField(
            model_name="serieview",
            name="user",
        ),
        migrations.AlterField(
            model_name="chapterimage",
            name="image",
            field=swatmanga.common.fields.WebPVersatileImageField(
                blank=True,
                null=True,
                upload_to=swatmanga.series.models.chapter_image_upload_to,
                validators=[
                    django.core.validators.FileExtensionValidator(
                        allowed_extensions=["jpeg", "jpg", "webp", "png", "gif"]
                    )
                ],
                verbose_name="Chapter Image",
            ),
        ),
        migrations.AlterField(
            model_name="serie",
            name="cover",
            field=swatmanga.common.fields.WebPVersatileImageField(
                blank=True,
                null=True,
                upload_to=swatmanga.series.models.serie_cover_upload_to,
                validators=[
                    django.core.validators.FileExtensionValidator(
                        allowed_extensions=["jpeg", "jpg", "webp", "png", "gif"]
                    )
                ],
                verbose_name="Cover Image",
            ),
        ),
        migrations.DeleteModel(
            name="ChapterView",
        ),
        migrations.DeleteModel(
            name="SerieView",
        ),
    ]
