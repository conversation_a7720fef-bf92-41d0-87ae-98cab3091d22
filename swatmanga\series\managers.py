from django.db import models


class SerieManager(models.Manager):

    def get_queryset(self):
        return super().get_queryset()

    def for_list(self):
        return (
            self.get_queryset()
            .select_related("type", "status")
            .prefetch_related("genres")
        )

    def for_details(self):
        """
        Optimized queryset for SerieDetailSerializer
        """
        return (
            self.get_queryset()
            .select_related(
                "type", "status", "author", "artist", "created_by", "updated_by"
            )
            .prefetch_related(
                "genres",
            )
        )
