# Generated by Django 5.0.8 on 2024-08-13 17:25

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("series", "0005_alter_chapter_number"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="ChapterRead",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
            ],
            options={
                "db_table": "chapter_reads",
            },
        ),
        migrations.AddConstraint(
            model_name="chapterimage",
            constraint=models.UniqueConstraint(
                fields=("chapter", "order"), name="chapter_image_order_uniq"
            ),
        ),
        migrations.AddConstraint(
            model_name="chapterview",
            constraint=models.UniqueConstraint(
                fields=("chapter", "user"), name="user_viewed_chapter_uniq"
            ),
        ),
        migrations.AddConstraint(
            model_name="favorite",
            constraint=models.UniqueConstraint(
                fields=("serie", "user"), name="user_fav_serie_uniq"
            ),
        ),
        migrations.AddConstraint(
            model_name="follow",
            constraint=models.UniqueConstraint(
                fields=("serie", "user"), name="user_follow_serie_uniq"
            ),
        ),
        migrations.AddConstraint(
            model_name="rating",
            constraint=models.UniqueConstraint(
                fields=("serie", "user"), name="user_rate_serie_uniq"
            ),
        ),
        migrations.AddConstraint(
            model_name="serieview",
            constraint=models.UniqueConstraint(
                fields=("serie", "user"), name="user_viewed_serie_uniq"
            ),
        ),
        migrations.AddField(
            model_name="chapterread",
            name="chapter",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="chapterreads",
                to="series.chapter",
            ),
        ),
        migrations.AddField(
            model_name="chapterread",
            name="user",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL
            ),
        ),
        migrations.AddConstraint(
            model_name="chapterread",
            constraint=models.UniqueConstraint(
                fields=("chapter", "user"), name="user_read_chapter_uniq"
            ),
        ),
    ]
