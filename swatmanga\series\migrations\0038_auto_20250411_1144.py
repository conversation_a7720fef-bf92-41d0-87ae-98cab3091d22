# Generated by Django 5.0.8 on 2025-04-11 11:36

from django.db import migrations

def set_initial_order(apps, schema_editor):
    Serie = apps.get_model('series', 'Serie')
    Chapter = apps.get_model('series', 'Chapter')
    for serie in Serie.objects.all():
        chapters = Chapter.objects.filter(serie=serie).order_by('created_at')
        for index, chapter in enumerate(chapters):
            chapter.order = index + 1
            chapter.save()
        print(f"Enumerated Serie ({serie.title}) Chapters ({len(chapters)})")

class Migration(migrations.Migration):

    dependencies = [
        ("series", "0037_alter_chapter_options_chapter_order"),
    ]

    operations = [
        migrations.RunPython(set_initial_order),
    ]

