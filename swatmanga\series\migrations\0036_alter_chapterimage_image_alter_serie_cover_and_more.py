# Generated by Django 5.0.8 on 2025-04-04 08:38

import django.core.validators
import swatmanga.common.fields
import swatmanga.series.models
from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ("series", "0035_alter_rating_rating"),
    ]

    operations = [
        migrations.AlterField(
            model_name="chapterimage",
            name="image",
            field=swatmanga.common.fields.WebPVersatileImageField(
                blank=True,
                max_length=255,
                null=True,
                upload_to=swatmanga.series.models.chapter_image_upload_to,
                validators=[
                    django.core.validators.FileExtensionValidator(
                        allowed_extensions=("jpeg", "jpg", "webp", "png", "gif")
                    )
                ],
                verbose_name="Chapter Image",
            ),
        ),
        migrations.AlterField(
            model_name="serie",
            name="cover",
            field=swatmanga.common.fields.WebPVersatileImageField(
                blank=True,
                null=True,
                upload_to=swatmanga.series.models.serie_cover_upload_to,
                validators=[
                    django.core.validators.FileExtensionValidator(
                        allowed_extensions=("jpeg", "jpg", "webp", "png", "gif")
                    )
                ],
                verbose_name="Cover Image",
            ),
        ),
        migrations.AlterField(
            model_name="serie",
            name="poster",
            field=swatmanga.common.fields.WebPVersatileImageField(
                blank=True,
                null=True,
                upload_to=swatmanga.series.models.serie_poster_upload_to,
                validators=[
                    django.core.validators.FileExtensionValidator(
                        allowed_extensions=("jpeg", "jpg", "webp", "png", "gif")
                    )
                ],
                verbose_name="Poster Image",
            ),
        ),
    ]
