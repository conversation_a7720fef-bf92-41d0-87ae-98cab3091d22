class RequireDefinedAttrs:
    required_attrs_list_name = "required_attrs"

    def __new__(cls, *args, **kwargs):
        assert hasattr(
            cls, cls.required_attrs_list_name
        ), f"Subclass `{cls.__name__}` should have `{cls.required_attrs_list_name}` list defined"

        for attr in cls.required_attrs:
            assert hasattr(
                cls, attr
            ), f"Subclass `{cls.__name__}` should have {attr} defined"

        return super().__new__(cls)


class GetSerializerClassMixin(RequireDefinedAttrs):
    # A class which inherits this mixins should have variable
    # `serializer_action_classes`.
    # Look for serializer class in self.serializer_action_classes, which
    # should be a dict mapping action name (key) to serializer class (value),
    # i.e.:
    # class SampleViewSet(viewsets.ViewSet):
    #     serializer_class = DocumentSerializer
    #     serializer_action_classes = {
    #        'upload': UploadDocumentSerializer,
    #        'download': DownloadDocumentSerializer,
    #     }
    #     @action
    #     def upload:
    #         ...
    # If there's no entry for that action then just fallback to the regular
    # get_serializer_class lookup: self.serializer_class, DefaultSerializer.

    required_attrs = ["serializer_action_classes"]

    def get_serializer_class(self):
        try:
            return self.serializer_action_classes[self.action]
        except (KeyError, AttributeError):
            return super().get_serializer_class()


class GetPermissionsMixin(RequireDefinedAttrs):
    # A class which inhertis this mixins should have variable
    # `permission_action_classes`.
    # Look for permission class in self.permission_action_classes, which
    # should be a dict mapping action name (key) to serializer class (value),
    # i.e.:
    # class SampleViewSet(viewsets.ViewSet):
    #     serializer_class = DocumentSerializer
    #     permission_action_classes = {
    #        'upload': IsAdmin,
    #        'download': IsAuthenticated,
    #     }
    #     @action
    #     def upload:
    #         ...
    # If there's no entry for that action then just fallback to the regular
    # get_permissions lookup: self.permission_classes, DefaultPermissions.

    required_attrs = ["permission_action_classes"]

    def get_permissions(self):
        try:
            permission_classes = self.permission_action_classes[self.action]
            assert isinstance(
                permission_classes, list
            ), f"{self.required_attrs[0]} should be a list of permission classes"
            return [p() for p in permission_classes]
        except (KeyError, AttributeError):
            return super().get_permissions()
