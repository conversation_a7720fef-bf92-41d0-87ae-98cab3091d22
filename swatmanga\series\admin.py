from django import forms
from django.contrib import admin
from django.utils.html import format_html
from django.db import models
from django.template.defaultfilters import truncatechars
from django_admin_inline_paginator.admin import TabularInlinePaginated, InlinePaginated
from django.contrib.admin import TabularInline
from admin_auto_filters.filters import AutocompleteFilter
from adminsortable2.admin import SortableInlineAdminMixin
from adminsortable2.admin import SortableAdminBase

from .models import (
    Chapter,
    ChapterImage,
    ChapterRead,
    Donation,
    Favorite,
    Follow,
    Genre,
    Rating,
    Serie,
    SerieArtist,
    SerieAuthor,
    SerieStatus,
    SerieType,
)

class SerieFilter(AutocompleteFilter):
    title = 'Serie'
    field_name = 'serie'

class ChapterFilter(AutocompleteFilter):
    title = 'Chapter'
    field_name = 'chapter'

class UserFilter(AutocompleteFilter):
    title = 'User'
    field_name = 'user'


class ChapterImageInlineForm(forms.ModelForm):
    class Meta:
        model = ChapterImage
        # Include only the fields you want to display
        fields = ["image", "order"]
        # Override the widget for the image field to use a simpler file input
        widgets = {
            "image": forms.FileInput(attrs={"accept": "image/*"}),
        }


# Inline Models
class ChapterImageInline(TabularInlinePaginated):
    model = ChapterImage
    fields = ("order", "image_preview", "image")
    readonly_fields = ("image_preview",)
    extra = 1
    form = ChapterImageInlineForm
    show_change_link = True
    per_page = 20

    def image_preview(self, obj):
        if obj.image:
            return format_html(
                '<a href="{0}" target="_blank">View Image</a>', obj.image.url
            )
        return "-"

    def get_queryset(self, request):
        return super().get_queryset(request).select_related("chapter")

    image_preview.short_description = "Image Link"


class ChapterInline(SortableInlineAdminMixin, InlinePaginated, TabularInline):
    model = Chapter
    fields = ("title", "chapter")
    readonly_fields = ("title", "chapter")
    show_change_link = True
    extra = 0
    per_page = 20
    template = "admin/edit_inline/sortable_paginated_tabular.html"


# Admin classes
@admin.register(SerieAuthor)
class SerieAuthorAdmin(admin.ModelAdmin):
    list_display = ("name",)
    search_fields = ("name",)


@admin.register(SerieArtist)
class SerieArtistAdmin(admin.ModelAdmin):
    list_display = ("name",)
    search_fields = ("name",)


@admin.register(SerieType)
class SerieTypeAdmin(admin.ModelAdmin):
    list_display = ("name",)
    search_fields = ("name",)


@admin.register(SerieStatus)
class SerieStatusAdmin(admin.ModelAdmin):
    list_display = ("name",)
    search_fields = ("name",)


@admin.register(Genre)
class GenreAdmin(admin.ModelAdmin):
    list_display = ("name",)
    search_fields = ("name",)


@admin.register(Serie)
class SerieAdmin(SortableAdminBase, admin.ModelAdmin):
    list_per_page = 20
    list_display = (
        "title",
        "slug",
        "author",
        "artist",
        "type",
        "status",
        "published",
        "is_hot",
        "views_count",
        "rating",
    )
    list_select_related = ("author", "artist", "type", "status")
    list_filter = ("type", "status", "is_hot", "allow_comments")
    search_fields = ("title", "alternative")
    prepopulated_fields = {"slug": ("title",)}
    readonly_fields = (
        "cover_preview",
        "poster_preview",
        "views_count",
        "rating",
        "ratings_count",
        "followers_count",
        "favorites_count",
        "donations_count",
        "chapters_count",
        "created_by",
        "updated_by",
        "created_at",
        "updated_at",
        "latest_chapter_updated_at",
    )
    date_hierarchy = "published"
    autocomplete_fields = ["author", "artist", "type", "status", "genres"]
    inlines = [ChapterInline]

    fieldsets = (
        (
            "Core Information",
            {
                "fields": ("title", "slug", "letter", "alternative"),
                "classes": ("wide", "extrapretty"),
            },
        ),
        (
            "Visual Assets",
            {
                "fields": ("poster", "poster_preview", "cover", "cover_preview"),
                "classes": ("collapse",),
                "description": "Series visual identity assets",
            },
        ),
        ("Story & Creators", {"fields": ("story", "author", "artist")}),
        (
            "Classification & Metadata",
            {
                "fields": ("official_rating", "type", "status", "genres"),
                "classes": ("collapse",),
            },
        ),
        ("Publication Info", {"fields": ("published", "is_hot", "allow_comments")}),
        (
            "Statistics & Tracking",
            {
                "fields": (
                    "views_count",
                    "rating",
                    "ratings_count",
                    "followers_count",
                    "favorites_count",
                    "donations_count",
                    "chapters_count",
                    "latest_chapter_updated_at",
                ),
                "classes": ("collapse",),
            },
        ),
        (
            "Moderation Tracking",
            {
                "fields": ("created_by", "created_at", "updated_by", "updated_at"),
                "classes": ("collapse",),
            },
        ),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).prefetch_related("genres")

    # Denormalized display methods to avoid joins
    def author_name(self, obj):
        return obj.author.name if obj.author else None

    def artist_name(self, obj):
        return obj.artist.name if obj.artist else None

    def type_name(self, obj):
        return obj.type.name if obj.type else None

    def status_name(self, obj):
        return obj.status.name if obj.status else None

    def cover_preview(self, obj):
        if obj.cover:
            return format_html(
                '<img src="{}" style="max-height: 200px;" />', 
                obj.cover.url
            )
        return "-"

    def poster_preview(self, obj):
        if obj.poster:
            return format_html(
                '<img src="{}" style="max-height: 200px;" />', 
                obj.poster.url
            )
        return "-"

    def save_model(self, request, obj, form, change):
        # Set created_by only when the object is created (not on update)
        if not change:
            obj.created_by = request.user
        super().save_model(request, obj, form, change)

    author_name.admin_order_field = "author__name"
    artist_name.admin_order_field = "artist__name"
    type_name.admin_order_field = "type__name"
    status_name.admin_order_field = "status__name"

    cover_preview.short_description = "Cover Preview"
    poster_preview.short_description = "Poster Preview"


@admin.register(Rating)
class RatingAdmin(admin.ModelAdmin):
    list_per_page = 20
    list_display = ("serie", "user", "rating")
    list_filter = (SerieFilter,UserFilter)
    search_fields = ("serie__title", "user__username")


@admin.register(Donation)
class DonationAdmin(admin.ModelAdmin):
    list_per_page = 20
    list_display = ("serie", "user", "coins")
    list_filter = (SerieFilter,UserFilter)
    search_fields = ("serie__title", "user__username")


@admin.register(Follow)
class FollowAdmin(admin.ModelAdmin):
    list_display = ("serie", "user")
    list_filter = (SerieFilter,UserFilter)
    search_fields = ("serie__title", "user__username")


@admin.register(Favorite)
class FavoriteAdmin(admin.ModelAdmin):
    list_display = ("serie", "user")
    list_filter = (SerieFilter,UserFilter)
    search_fields = ("serie__title", "user__username")


@admin.register(Chapter)
class ChapterAdmin(admin.ModelAdmin):
    list_per_page = 20
    ordering = ("-created_at",)
    list_display = (
        "truncated_title",
        "chapter",
        "serie_title",
        "published",
        "views_count",
        "images_count",
    )
    list_select_related = ("serie",)
    list_filter = (SerieFilter,"allow_comments")
    search_fields = ("title", "serie__title")
    prepopulated_fields = {"slug": ("title",)}
    readonly_fields = (
        "created_by",
        "updated_by",
        "views_count",
        "images_count",
        "created_at",
        "updated_at",
    )
    inlines = [ChapterImageInline]
    date_hierarchy = "published"
    autocomplete_fields = ["serie"]
    show_full_result_count = False
    fieldsets = (
        ("Chapter Info", {"fields": ("title", "slug", "chapter", "order")}),
        ("Associated Content", {"fields": ("serie",)}),
        ("Publication Details", {"fields": ("published", "allow_comments")}),
        (
            "Statistics",
            {
                "fields": ("views_count", "images_count"),
                "classes": ("collapse",),
            },
        ),
        (
            "Moderation Tracking",
            {
                "fields": ("created_by", "created_at", "updated_by", "updated_at"),
                "classes": ("collapse",),
            },
        ),
    )

    def get_queryset(self, request):
        return (
            super()
            .get_queryset(request)
            .annotate(images_count_db=models.Count("images"))
        )

    def images_count(self, obj):
        return obj.images_count_db

    images_count.admin_order_field = "images_count_db"

    def serie_title(self, obj):
        return obj.serie.title

    def truncated_title(self, obj):
        return truncatechars(obj.title, 30)

    def save_model(self, request, obj, form, change):
        # Set created_by only when the object is created (not on update)
        if not change:
            obj.created_by = request.user
        super().save_model(request, obj, form, change)


    serie_title.admin_order_field = "serie__title"
    truncated_title.short_description = "Title"


@admin.register(ChapterRead)
class ChapterReadAdmin(admin.ModelAdmin):
    list_per_page = 20
    list_display = ("truncated_chapter", "user")
    list_select_related = ("chapter", "user")
    list_filter = [ChapterFilter, UserFilter]

    def truncated_chapter(self, obj):
        return truncatechars(obj.chapter.title, 30)
