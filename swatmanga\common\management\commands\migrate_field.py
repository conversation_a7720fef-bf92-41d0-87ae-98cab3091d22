from sqlalchemy import create_engine, text
from django.core.management.base import BaseCommand
from django.db import connections
from django.db.utils import OperationalError
from urllib.parse import urlparse

class Command(BaseCommand):
    help = 'Migrate ratings from old MySQL database to PostgreSQL'

    def add_arguments(self, parser):
        parser.add_argument(
            '--mysql-url',
            type=str,
            required=True,
            help='MySQL connection URL (e.g.: mysql://user:password@host:port/dbname)'
        )
        parser.add_argument(
            '--source-table',
            type=str,
            required=True,
            help='Source table name in MySQL'
        )
        parser.add_argument(
            '--source-field',
            type=str,
            required=True,
            help='Source field name in MySQL'
        )
        parser.add_argument(
            '--target-table',
            type=str,
            required=True,
            help='Target table name in PostgreSQL (default: use Django model)'
        )
        parser.add_argument(
            '--target-field',
            type=str,
            required=True,
            help='Target field name in PostgreSQL'
        )

    def handle(self, *args, **options):
        # Parse MySQL connection URL
        mysql_url = options['mysql_url']
        # Connect to MySQL
        try:
            self.stdout.write(self.style.NOTICE('Connecting to MySQL...'))
            mysql_engine = create_engine(mysql_url, connect_args={"client_flag": 0})
            mysql_conn = mysql_engine.connect()
        except OperationalError as e:
            self.stdout.write(self.style.ERROR(f'MySQL connection failed: {e}'))
            return

        # Get PostgreSQL connection
        postgres_conn = connections['default']
        
        try:
            # Fetch data from MySQL
            self.stdout.write(self.style.NOTICE(f'Reading data from MySQL table {options["source_table"]}...'))
            mysql_cur = mysql_conn.execute(text(f'SELECT id, {options["source_field"]} FROM {options["source_table"]}'))
            mysql_data = {row[0]: (row[1] or 0.0) for row in mysql_cur.fetchall()}

            model = None
            table_name = options['target_table']
            # Update PostgreSQL records
            self.stdout.write(self.style.NOTICE(f'Updating {table_name} in PostgreSQL...'))
            
            if model:
                # Use Django ORM if model is specified
                total = 0
                for obj in model.objects.all():
                    if obj.id in mysql_data:
                        setattr(obj, options['target_field'], mysql_data[obj.id])
                        obj.save()
                        total += 1
                self.stdout.write(self.style.SUCCESS(f'Updated {total} records using ORM'))
            else:
                # Use raw SQL if target table is specified
                with postgres_conn.cursor() as pg_cursor:
                    total = 0
                    for record_id, value in mysql_data.items():
                        pg_cursor.execute(
                            f'UPDATE {table_name} SET {options["target_field"]} = %s WHERE id = %s',
                            [value, record_id]
                        )
                        total += pg_cursor.rowcount
                    self.stdout.write(self.style.SUCCESS(f'Updated {total} records using raw SQL'))

        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Error during migration: {e}'))
        finally:
            mysql_conn.close()
            self.stdout.write(self.style.NOTICE('MySQL connection closed'))
