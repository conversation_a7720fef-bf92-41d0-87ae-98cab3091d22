volumes:
  production_postgres_data: {}
  production_postgres_data_backups: {}
  production_traefik: {}
  production_django_media: {}



services:
  django:
    build:
      context: .
      dockerfile: ./compose/production/django/Dockerfile

    image: swatmanga_production_django
    volumes:
      - production_django_media:/app/swatmanga/media
    depends_on:
      - postgres
      - redis
    env_file:
      - ./.envs/.production/.django
      - ./.envs/.production/.postgres
    command: /start

  postgres:
    build:
      context: .
      dockerfile: ./compose/production/postgres/Dockerfile
    image: swatmanga_production_postgres
    volumes:
      - production_postgres_data:/var/lib/postgresql/data
      - production_postgres_data_backups:/backups
    env_file:
      - ./.envs/.production/.postgres

  traefik:
    build:
      context: .
      dockerfile: ./compose/production/traefik/Dockerfile
    image: swatmanga_production_traefik
    depends_on:
      - django
    volumes:
      - production_traefik:/etc/traefik/acme
    ports:
      - '0.0.0.0:80:80'
      - '0.0.0.0:443:443'

  redis:
    image: docker.io/redis:6


  nginx:
    build:
      context: .
      dockerfile: ./compose/production/nginx/Dockerfile
    image: swatmanga_production_nginx
    depends_on:
      - django
    volumes:
      - production_django_media:/usr/share/nginx/media:ro
