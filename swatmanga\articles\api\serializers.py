from rest_framework import serializers

from swatmanga.common.api.serializers import CustomVersatileImageFieldSerializer
from ..models import Article
from swatmanga.users.api.serializers import UserSerializer
from taggit.serializers import (TagListSerializerField,
                                TaggitSerializer)
from babel.dates import format_date
from django.utils.html import strip_tags
from django.utils.text import Truncator

SUMMARY_LENGTH = 197 # saving 3 chars for ellipsis

class ArticleSerializer(TaggitSerializer,serializers.ModelSerializer):
    tags = TagListSerializerField()
    cover = CustomVersatileImageFieldSerializer(sizes="series_cover", read_only=True)
    created_by = UserSerializer(read_only=True, allow_null=True)
    updated_by = UserSerializer(read_only=True, allow_null=True)
    class Meta:
        model = Article
        fields = [
            "id",
            "title",
            "slug",
            "cover",
            "tags",
            "is_hot",
            "allow_comments",
            "content",
            "created_at",
            "created_by",
            "updated_at",
            "updated_by",
        ]

class ArticleListSerializer(TaggitSerializer,serializers.ModelSerializer):
    summary = serializers.SerializerMethodField()
    cover = CustomVersatileImageFieldSerializer(sizes="series_cover", read_only=True)
    created_by = UserSerializer(read_only=True, allow_null=True)
    created_at_humanized = serializers.SerializerMethodField()
    class Meta:
        model = Article
        fields = [
            "id",
            "title",
            "slug",
            "cover",
            "is_hot",
            "summary",
            "created_at",
            "created_at_humanized",
            "created_by",
        ]


    def get_created_at_humanized(self, obj: Article):
        datetime = obj.created_at.replace(tzinfo=None)
        return format_date(datetime, format='long', locale="ar")

    def get_summary(self, obj):
        # Remove HTML tags
        plain_text = strip_tags(obj.content)
         
        truncated = Truncator(plain_text).chars(SUMMARY_LENGTH)
        return truncated
