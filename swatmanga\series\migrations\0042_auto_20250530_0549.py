# Generated by Django 5.0.8 on 2025-05-30 05:49

from django.db import migrations

def set_defaults(apps, schema_editor):
    Serie = apps.get_model("series", "Serie")
    SerieType = apps.get_model("series", "SerieType")
    SerieStatus = apps.get_model("series", "SerieStatus")
    default_type = None
    default_status = None

    try:
        default_type = SerieType.objects.get(name__iexact="manhwa")
    except SerieType.DoesNotExist:
        # raise Exception("SerieType with name 'manhwa' does not exist.")
        pass

    try:
        default_status = SerieStatus.objects.get(name__iexact="ongoing")
    except SerieStatus.DoesNotExist:
        # raise Exception("SerieStatus with name 'ongoing' does not exist.")
        pass

    if default_status and default_type:
        Serie.objects.filter(type__isnull=True).update(type=default_type)
        # Update null status
        Serie.objects.filter(status__isnull=True).update(status=default_status)


class Migration(migrations.Migration):

    dependencies = [
        ("series", "0041_alter_serie_official_rating_alter_serie_rating"),
    ]

    operations = [
        migrations.RunPython(set_defaults, reverse_code=migrations.RunPython.noop),
    ]
