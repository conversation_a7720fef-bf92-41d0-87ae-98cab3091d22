import os

from computedfields.models import ComputedFieldsModel, computed
from django.conf import settings
from django.contrib.contenttypes.fields import GenericRelation
from django.contrib.postgres.fields import ArrayField
from django.core.validators import FileExtensionValidator
from django.db import models
from django.db.utils import IntegrityError
from django.utils.text import slugify
from django.utils.translation import gettext_lazy as _
from hitcount.models import HitCount, HitCountMixin
from typing import Self
from django.db.models import Avg
from django.core.validators import MinValueValidator, MaxValueValidator
from django.db.models import Max


from swatmanga.common.fields import WebPVersatileImageField
from swatmanga.common.utils import dash2underscore
from swatmanga.series.managers import SerieManager
from swatmanga.users.models import User


ADMIN_RATING_WEIGHT = 0.7 # 70%

# Utility function to build file paths
def serie_cover_upload_to(instance, filename):
    # Builds the path: 'series/serie_name/cover.webp'
    return os.path.join("series", dash2underscore(instance.slug), "cover.webp")


def serie_poster_upload_to(instance, filename):
    # Builds the path: 'series/serie_name/poster.webp'
    return os.path.join("series", dash2underscore(instance.slug), "poster.webp")


def chapter_image_upload_to(instance, filename):
    # Builds the path: 'series/serie_name/chapters/chapter_number/image_filename'
    return os.path.join(
        "series",
        dash2underscore(instance.chapter.serie.slug),
        "chapters",
        f"{instance.chapter.chapter:04}",
        f"{instance.order:04}.webp",
    )


class SerieAuthor(models.Model):
    """
    Model representing an author of a serie
    """

    name = models.CharField(max_length=100, unique=True)

    class Meta:
        db_table = "serie_authors"

    def __str__(self):
        return self.name


class SerieArtist(models.Model):
    """
    Model representing an artist of a serie
    """

    name = models.CharField(max_length=100, unique=True)

    class Meta:
        db_table = "serie_artists"

    def __str__(self):
        return self.name


class SerieType(models.Model):
    """
    Model representing the type of a series (e.g., Manga, Novel).
    """

    name = models.CharField(max_length=30, unique=True)

    class Meta:
        db_table = "serie_types"

    def __str__(self):
        return self.name


class SerieStatus(models.Model):
    """
    Model representing the status of a series, supporting translation.
    """

    name = models.CharField(max_length=30, unique=True)

    class Meta:
        db_table = "serie_statuses"

    def __str__(self):
        return self.name


class Genre(ComputedFieldsModel):
    """
    Model representing a genre that can be associated with a series.
    """

    name = models.CharField(max_length=50, unique=True)

    @computed(
        models.IntegerField(default=0, db_default=0),
        depends=[("series", ["id"])],
        querysize=1,
    )
    def series_count(self) -> int:
        if not self.id:
            return 0
        return self.series.count()

    class Meta:
        db_table = "genres"

    def __str__(self):
        return self.name


class Serie(ComputedFieldsModel, HitCountMixin):
    """
    Model representing a series.
    """

    objects = SerieManager()

    # Core Identifiers
    title = models.CharField(max_length=255)
    slug = models.SlugField(max_length=255, unique=True, auto_created=True)
    letter = models.CharField(
        max_length=1, null=True, blank=True
    )  # Used for alphabetical sorting or categorization

    # Visual & Descriptive Fields
    poster = WebPVersatileImageField(
        "Poster Image",
        upload_to=serie_poster_upload_to,
        blank=True,
        null=True,
        lossy_quality=93,
        validators=[
            FileExtensionValidator(allowed_extensions=settings.ALLOWED_IMAGE_EXTENSIONS)
        ],
    )

    cover = WebPVersatileImageField(
        "Cover Image",
        upload_to=serie_cover_upload_to,
        blank=True,
        null=True,
        lossy_quality=93,
        validators=[
            FileExtensionValidator(allowed_extensions=settings.ALLOWED_IMAGE_EXTENSIONS)
        ],
    )

    alternative = models.CharField(
        max_length=255, null=True, blank=True
    )  # Alternative titles
    story = models.TextField(max_length=5000)
    author = models.ForeignKey(
        SerieAuthor, on_delete=models.DO_NOTHING, null=True, blank=True
    )
    artist = models.ForeignKey(
        SerieArtist, on_delete=models.DO_NOTHING, null=True, blank=True
    )

    # Classification & Metadata
    type = models.ForeignKey(
        SerieType,
        on_delete=models.DO_NOTHING,
        null=False,
        blank=False,
        related_name="series",
    )
    status = models.ForeignKey(
        SerieStatus,
        on_delete=models.DO_NOTHING,
        null=False,
        blank=False,
        related_name="series",
    )
    genres = models.ManyToManyField(Genre, related_name="series", blank=False)
    published = models.DateField(null=True, blank=True)
    official_rating = models.DecimalField(
        max_digits=3, 
        decimal_places=1, 
        default=0.0, 
        db_default=0.0,
        help_text=f"{ADMIN_RATING_WEIGHT * 100}% Weights of rating"
    )

    allow_comments = models.BooleanField("allow comments", default=True)

    hit_count_generic = GenericRelation(
        HitCount,
        object_id_field="object_pk",
        related_query_name="hit_count_generic_relation",
    )

    latest_chapter_updated_at = models.DateTimeField(
        null=True, blank=True, db_index=True
    )

    # Computed Fields (stored in the database and flushed every <querysize> queries)
    @computed(
        models.IntegerField(default=0, db_default=0),
        depends=[("hit_count_generic", ["content_type", "object_pk", "hits"])],
        querysize=20,
    )
    def views_count(self) -> int:
        try:
            hits = self.hit_count.hits
        # workaround for factory boy
        except (HitCount.DoesNotExist, IntegrityError):
            hits = 0
        return hits

    @computed(
        models.DecimalField(
            max_digits=3, decimal_places=1, default=0.0, db_default=0.0, help_text=f"A combination of `official_rating` and users ratings"
        ),
        depends=[
            ("ratings", ["serie"]),    # Dependency on user ratings
            ("self", ["official_rating"])  # Dependency on official_rating field
        ],
        querysize=10,
    )
    def rating(self) -> float:
        if not self.id:
            return 0.0
        user_avg = self.ratings.aggregate(avg=Avg("rating"))["avg"]
        if user_avg is None:
            # No user ratings available, return official rating
            return self.official_rating
        else:
            # Combine official weighted rating with user average 
            combined_rating = (float(self.official_rating) * ADMIN_RATING_WEIGHT) + (user_avg * (1-ADMIN_RATING_WEIGHT))
            return round(combined_rating, 1)

    @computed(
        models.IntegerField(default=0, db_default=0),
        depends=[("ratings", ["serie"])],
        querysize=10,
    )
    def ratings_count(self) -> int:
        # Logic to calculate or retrieve ratings count
        if not self.id:
            return 0
        return self.ratings.count()

    @computed(
        models.IntegerField(default=0, db_default=0),
        depends=[("follows", ["serie"])],
        querysize=10,
    )
    def followers_count(self) -> int:
        # Logic to calculate or retrieve follow count
        if not self.id:
            return 0
        return self.follows.count()

    @computed(
        models.IntegerField(default=0, db_default=0),
        depends=[("favorites", ["serie"])],
        querysize=10,
    )
    def favorites_count(self) -> int:
        # Logic to calculate or retrieve follow count
        if not self.id:
            return 0
        return self.favorites.count()

    @computed(
        models.IntegerField(default=0, db_default=0),
        depends=[("donations", ["serie"])],
        querysize=1,
    )
    def donations_count(self) -> float:
        # Logic to calculate or retrieve donations
        if not self.id:
            return 0
        return self.donations.count()

    @computed(
        models.IntegerField(default=0, db_default=0),
        depends=[("chapters", ["serie"])],
        querysize=1,
    )
    def chapters_count(self) -> int:
        # Logic to calculate or retrieve chapter count
        if not self.id:
            return 0
        return self.chapters.count()

    # Miscellaneous
    is_hot = models.BooleanField(
        default=False, db_index=True
    )  # Flag for trending series

    # Records
    created_at = models.DateTimeField(auto_now_add=True)
    created_by: User = models.ForeignKey(
        User, on_delete=models.CASCADE, related_name="created_series", null=True
    )

    updated_at = models.DateTimeField(auto_now=True, null=True)
    updated_by: User = models.ForeignKey(User, on_delete=models.CASCADE, null=True)

    @property
    def thumbnail(self):
        try:
            return self.poster.thumbnail['200x200']
        except:
            return None

    class Meta:
        db_table = "series"

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(self.title)
        if not self.letter:
            self.letter = self.title[0].upper()
        super().save(*args, **kwargs)

    def __str__(self):
        return self.title


class Rating(models.Model):
    """
    Model representing a user who rates the series.
    """

    serie: Serie = models.ForeignKey(
        "Serie", on_delete=models.CASCADE, related_name="ratings"
    )
    user: User = models.ForeignKey(
        User, on_delete=models.CASCADE, related_name="ratings"
    )
    rating: int = models.PositiveSmallIntegerField(
        validators=[MinValueValidator(1), MaxValueValidator(10)]
    )  # Rating out of 10 or 5, etc.

    class Meta:
        db_table = "ratings"
        constraints = [
            models.UniqueConstraint(
                fields=["serie", "user"], name="user_rate_serie_uniq"
            )
        ]

    def __str__(self):
        return f"{self.user} rated {self.rating} to {self.serie}"


class Donation(models.Model):
    """
    Model representing a user who donates to the series.
    """

    serie: Serie = models.ForeignKey(
        "Serie", on_delete=models.CASCADE, related_name="donations"
    )
    user: User = models.ForeignKey(
        User, on_delete=models.CASCADE, related_name="donations"
    )
    coins: float = models.IntegerField()

    class Meta:
        db_table = "donations"


class Follow(models.Model):
    """
    Model representing a user who follows the series.
    """

    serie: Serie = models.ForeignKey(
        "Serie", on_delete=models.CASCADE, related_name="follows"
    )
    user: User = models.ForeignKey(
        User, on_delete=models.CASCADE, related_name="follows"
    )

    class Meta:
        db_table = "follows"
        constraints = [
            models.UniqueConstraint(
                fields=["serie", "user"], name="user_follow_serie_uniq"
            )
        ]


class Favorite(models.Model):
    """
    Model representing a serie which is a favorite for user.
    """

    serie: Serie = models.ForeignKey(
        "Serie", on_delete=models.CASCADE, related_name="favorites"
    )
    user: User = models.ForeignKey(
        User, on_delete=models.CASCADE, related_name="favorites"
    )

    class Meta:
        db_table = "favorites"
        constraints = [
            models.UniqueConstraint(
                fields=["serie", "user"], name="user_fav_serie_uniq"
            )
        ]


class Chapter(ComputedFieldsModel, HitCountMixin):
    """
    Model representing a serie's chapter.
    """

    title = models.CharField(max_length=255)
    slug = models.CharField(max_length=255)
    chapter = models.CharField(
        max_length=255, db_index=True
    )  # for backward compatibility
    serie: Serie = models.ForeignKey(
        Serie,
        on_delete=models.CASCADE,
        related_name="chapters",
    )
    order = models.PositiveIntegerField(default=0, db_index=True, blank=False, null=False)
    published = models.DateField(null=True, blank=True)

    allow_comments = models.BooleanField("allow comments", default=True)

    hit_count_generic = GenericRelation(
        HitCount,
        object_id_field="object_pk",
        related_query_name="hit_count_generic_relation",
    )

    # Records
    created_at = models.DateTimeField(auto_now_add=True, editable=True)
    created_by: User = models.ForeignKey(
        User, on_delete=models.CASCADE, related_name="created_chapters", null=True
    )

    updated_at = models.DateTimeField(auto_now=True, null=True, db_index=True)
    updated_by: User = models.ForeignKey(User, on_delete=models.CASCADE, null=True)
    _is_read: bool = False

    # Computed Fields (stored in the database)
    @computed(
        models.IntegerField(db_default=0),
        depends=[("hit_count_generic", ["content_type", "object_pk", "hits"])],
        querysize=20,
    )
    def views_count(self) -> int:
        try:
            hits = self.hit_count.hits
        # workaround for factory boy
        except (HitCount.DoesNotExist, IntegrityError):
            hits = 0
        return hits

    @computed(
        models.IntegerField(db_default=0),
        depends=[("images", ["chapter"])],
        querysize=1,
    )
    def images_count(self) -> int:
        try:
            im_cnt = self.images.count()
        # workaround for factory boy
        except (ValueError, ChapterImage.DoesNotExist):
            im_cnt = 0
        return im_cnt

    class Meta:
        db_table = "chapters"
        ordering = ["-order", "-created_at"]
        constraints = [
            models.UniqueConstraint(
                fields=["serie", "chapter"], name="serie_chapter_uniq"
            )
        ]

    @property
    def is_read(self):
        return self._is_read

    @is_read.setter
    def is_read(self, value):
        self._is_read = value

    @property
    def next_chapter(self) -> Self:
        """
        Returns the ID of the next chapter in the same series, ordered by created_at.
        """
        return (
            Chapter.objects.filter(
                serie=self.serie,
                created_at__gt=self.created_at,
            )
            .order_by("created_at")
            .first()
        )

    @property
    def previous_chapter(self) -> Self:
        """
        Returns the ID of the previous chapter in the same series, ordered by created_at.
        """
        return (
            Chapter.objects.filter(
                serie=self.serie,
                created_at__lt=self.created_at,
            )
            .order_by("-created_at")
            .first()
        )

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(self.title)
        # Auto-set order only for NEW chapters (not updates)
        if not self.pk:  # If the chapter is being created (not updated)
            # Get the max order value for chapters in the same serie
            max_order = Chapter.objects.filter(
                serie=self.serie
            ).aggregate(Max('order'))['order__max']

            # Set order to max_order + 1 (or 1 if no chapters exist)
            self.order = max_order + 1 if max_order is not None else 1

        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.serie.title} ch:{self.chapter}"


class ChapterImage(models.Model):
    chapter: Chapter = models.ForeignKey(
        Chapter, on_delete=models.CASCADE, related_name="images"
    )
    image = WebPVersatileImageField(
        "Chapter Image",
        max_length=255,
        upload_to=chapter_image_upload_to,
        blank=True,
        null=True,
        lossy_quality=85,
        validators=[
            FileExtensionValidator(allowed_extensions=settings.ALLOWED_IMAGE_EXTENSIONS)
        ],
    )
    order = models.PositiveIntegerField()

    # Records
    created_at = models.DateTimeField(auto_now_add=True, null=True)
    updated_at = models.DateTimeField(auto_now=True, null=True)

    class Meta:
        db_table = "chapter_images"
        ordering = ["chapter", "order"]
        constraints = [
            models.UniqueConstraint(
                fields=["chapter", "order"], name="chapter_image_order_uniq"
            )
        ]

    def __str__(self):
        return f"Image {self.order} for {self.chapter.title}"


class ChapterRead(models.Model):
    """
    Model representing a user who read a chapter.
    """

    chapter: Chapter = models.ForeignKey(
        "Chapter", on_delete=models.CASCADE, related_name="chapterreads"
    )
    user: User = models.ForeignKey(User, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True, db_index=True)

    class Meta:
        db_table = "chapter_reads"
        constraints = [
            models.UniqueConstraint(
                fields=["chapter", "user"], name="user_read_chapter_uniq"
            )
        ]


class Release(models.Model):
    serie_id = models.IntegerField(primary_key=True)
    title = models.CharField(max_length=255)
    latest_chapter_updated_at = models.DateTimeField()
    slug = models.CharField(max_length=255)
    type = models.JSONField()
    status = models.JSONField()
    genres = models.JSONField()
    poster = WebPVersatileImageField()
    is_hot = models.BooleanField()
    views_count = models.IntegerField()
    rating = models.DecimalField(max_digits=3, decimal_places=1)
    chapters = models.JSONField()

    class Meta:
        managed = False
        db_table = "releases"
