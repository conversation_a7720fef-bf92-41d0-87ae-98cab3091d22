from rest_framework import viewsets
from rest_framework.permissions import AllowAny

from swatmanga.common.api.mixins import GetPermissionsMixin
from swatmanga.common.api.permissions import NotBlackListed
from ..models import Article
from .serializers import ArticleListSerializer, ArticleSerializer
from .filters import ArticleFilterSet
from django_filters.rest_framework import DjangoFilterBackend
from django.utils.decorators import method_decorator
from django.views.decorators.cache import cache_page

@method_decorator(cache_page(60 * 60 * 24, key_prefix="articles.list"), name='list')
@method_decorator(cache_page(60 * 60 * 24, key_prefix="articles.detail"), name='retrieve')
class ArticleViewSet(viewsets.ReadOnlyModelViewSet):
    queryset = Article.objects.filter(public=True).order_by('-created_at').all()
    permission_classes = [NotBlackListed]
    serializer_class = ArticleSerializer
    filter_backends = [DjangoFilterBackend]
    filterset_class = ArticleFilterSet

    def get_serializer_class(self):
        if self.action == 'list':
            return ArticleListSerializer
        return self.serializer_class

# class ArticleViewSet(GetPermissionsMixin, viewsets.ModelViewSet):
#     queryset = Article.objects.filter(public=True).all()
#     permission_classes = [NotBlackListed]
#     permission_action_classes = {
#         "create": [ManageSeriesPermission | IsAdminUser],
#         "update": [ManageSeriesPermission | IsAdminUser],
#         "destroy": [ManageSeriesPermission | IsAdminUser],
#     }
#     serializer_class = ArticleSerializer
#     filter_backends = [DjangoFilterBackend]
#     filterset_class = ArticleFilterSet

