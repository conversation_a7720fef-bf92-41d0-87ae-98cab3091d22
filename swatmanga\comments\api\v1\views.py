from django.contrib.contenttypes.models import ContentType
from django.db.models import Count, Prefetch, Q
from django.core.cache import cache
from django.http import Http404, HttpResponseForbidden
from django_comments.models import CommentFlag
from django_comments_xtd.models import (
    DISLIKEDIT_FLAG,
    LIKEDIT_FLAG,
    MaxThreadLevelExceededException,
    XtdComment,
)
from django_comments_xtd.utils import get_app_model_options, get_current_site_id
from django_comments_xtd.views import perform_dislike, perform_flag, perform_like
from django_filters.rest_framework import DjangoFilterBackend
from drf_spectacular.utils import extend_schema, extend_schema_view
from rest_framework import status
from rest_framework.decorators import action
from rest_framework.exceptions import NotAuthenticated, NotFound
from rest_framework.permissions import IsAuthenticatedOrReadOnly
from rest_framework.response import Response
from rest_framework.viewsets import ModelViewSet

from swatmanga.common.api.mixins import GetSerializerClassMixin
from swatmanga.common.api.permissions import NotBlackListed
from swatmanga.common.utils import generate_cache_key, make_cache_key_user_unique

from .filters import CommentFilterSet
from .serializers import (
    CommentReplySerializer,
    CommentsCountSerializer,
    FlagSerializer,
    FlagSerializerSchema,
    ReadCommentSerializer,
    ReadCommentSerializerSchema,
    WriteCommentSerializer,
    WriteCommentSerializerSchema,
)


@extend_schema_view(
    create=extend_schema(
        request=WriteCommentSerializerSchema,
        responses={201: ReadCommentSerializerSchema, 403: None, 404: None, 400: None},
    ),
    update=extend_schema(
        request=WriteCommentSerializerSchema,
        responses={200: ReadCommentSerializerSchema, 403: None, 404: None, 400: None},
    ),
    partial_update=extend_schema(
        request=WriteCommentSerializerSchema,
        responses={200: ReadCommentSerializerSchema, 403: None, 404: None, 400: None},
    ),
    count=extend_schema(responses={200: CommentsCountSerializer, 404: None}),
    retrieve=extend_schema(responses={200: ReadCommentSerializerSchema, 404: None}),
    list=extend_schema(
        responses={200: ReadCommentSerializerSchema(many=True), 404: None}
    ),
    content_type_comments=extend_schema(
        responses={200: ReadCommentSerializerSchema(many=True), 404: None}
    ),
    destory=extend_schema(
        responses={200: ReadCommentSerializerSchema, 404: None, 403: None}
    ),
    feedback=extend_schema(
        responses={201: FlagSerializer, 204: None, 404: None, 403: None, 400: None},
        request=FlagSerializerSchema,
    ),
    reply=extend_schema(
        responses={201: ReadCommentSerializerSchema, 404: None, 403: None},
        request=CommentReplySerializer,
    ),
)
class CommentViewSet(GetSerializerClassMixin, ModelViewSet):
    queryset = XtdComment.objects.all()
    serializer_class = ReadCommentSerializer
    permission_classes = [IsAuthenticatedOrReadOnly & NotBlackListed]
    lookup_field = "pk"
    filter_backends = [DjangoFilterBackend]
    filterset_class = CommentFilterSet

    serializer_action_classes = {
        "retrieve": ReadCommentSerializer,
        "create": WriteCommentSerializer,
        "update": WriteCommentSerializer,
        "count": CommentsCountSerializer,
        "feedback": FlagSerializer,
        "reply": CommentReplySerializer,
    }

    def get_queryset(self):
        if self.action == "count":
            content_type_arg = self.kwargs.get("content_type", None)
            object_pk_arg = self.kwargs.get("object_pk", None)
            app_label, model = content_type_arg.split("-")
            content_type = ContentType.objects.get_by_natural_key(app_label, model)
            qs = XtdComment.objects.filter(
                content_type=content_type, object_pk=object_pk_arg, is_public=True
            )
            return qs
        else:
            qs = super().get_queryset()
            return qs.annotate(
                likes_count=Count("flags", filter=Q(flags__flag=LIKEDIT_FLAG)),
                dislikes_count=Count("flags", filter=Q(flags__flag=DISLIKEDIT_FLAG)),
            )

    @action(
        methods=["GET"],
        detail=False,
        url_path=r"(?P<content_type>series-chapter|series-serie|articles-article)/(?P<object_pk>[-\w]+)",
    )
    def content_type_comments(self, request, content_type, object_pk):
        """Get content-type comments, supported content types (series-chapter, series-serie)"""
        content_type_arg = content_type
        object_pk_arg = object_pk
        app_label, model = content_type_arg.split("-")

        # Caching
        cache_key = generate_cache_key(request, f'comments_{app_label}.{model}_{object_pk}')
        cache_key = make_cache_key_user_unique(request, cache_key)

        cached_data = cache.get(cache_key)
        if cached_data is not None:
            return Response(cached_data)


        try:
            content_type = ContentType.objects.get_by_natural_key(app_label, model)
        except ContentType.DoesNotExist:
            raise NotFound("Unsupported content-type")
        else:
            flags_qs = CommentFlag.objects.filter(
                flag__in=[LIKEDIT_FLAG, DISLIKEDIT_FLAG]
            ).prefetch_related("user")
            prefetch = Prefetch("flags", queryset=flags_qs)
            qs = (
                self.get_queryset()
                .prefetch_related(prefetch)
                .filter(
                    content_type=content_type,
                    object_pk=object_pk_arg,
                    site__pk=get_current_site_id(self.request),
                    is_public=True,
                )
            )
        qs = self.filter_queryset(queryset=qs)
        qs = self.paginate_queryset(qs)
        serializer = self.get_serializer(qs, many=True)
        response = self.get_paginated_response(serializer.data)
        cache.set(cache_key, response.data, 60 * 60)
        return response

    @action(
        methods=["GET"],
        detail=False,
        url_path=r"(?P<content_type>\w+-\w+)/(?P<object_pk>[-\w]+)/count",
    )
    def count(self, request, content_type, object_pk):
        data = {"count": self.get_queryset().count()}
        serializer = self.get_serializer(data=data)
        serializer.is_valid(raise_exception=True)
        return Response(serializer.data)

    @action(methods=["POST"], detail=True)
    def feedback(self, request, pk):
        """This method toggles like, dislike and report for a comment."""
        data = dict(request.data)
        data["comment"] = pk
        serializer = self.get_serializer(data=data)
        serializer.is_valid(raise_exception=True)
        flag = serializer._kwargs["data"]["flag"]
        perform_action = {
            "like": perform_like,
            "dislike": perform_dislike,
            "report": perform_flag,
        }.get(flag)
        created = perform_action(request, serializer.validated_data["comment"])
        if created:
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        else:
            return Response(status=status.HTTP_204_NO_CONTENT)

    @action(methods=["POST"], detail=True)
    def reply(self, request, pk):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        comment_body = serializer.validated_data["comment"]
        comment = reply_to_comment(request, pk, comment_body)
        return Response(comment, status=status.HTTP_201_CREATED)


def reply_to_comment(request, cid, comment_body):
    try:
        comment = XtdComment.objects.get(pk=cid)
        if not comment.allow_thread():
            raise MaxThreadLevelExceededException(comment)
    except MaxThreadLevelExceededException as exc:
        return HttpResponseForbidden(exc)
    except XtdComment.DoesNotExist as exc:
        raise Http404(exc)

    ct_str = comment.content_object._meta.label_lower
    options = get_app_model_options(content_type=ct_str)

    if not request.user.is_authenticated and options["who_can_post"] == "users":
        raise NotAuthenticated

    data = {
        "content_type": ct_str,
        "object_pk": comment.content_object.id,
        "comment": comment_body,
        "reply_to": cid,
    }
    serializer = WriteCommentSerializer(data=data, context={"request": request})
    serializer.is_valid(raise_exception=True)
    serializer.save()
    return serializer.data
