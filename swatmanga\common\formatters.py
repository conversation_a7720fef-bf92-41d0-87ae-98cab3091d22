from pythonjsonlogger.jsonlogger import JsonFormatter
import json
from django.http import HttpRequest
from socket import socket

def json_default(obj):
    # Handle Django WSGIRequest
    if isinstance(obj, HttpRequest):
        return {
            "type": "WSGIRequest",
            "method": obj.method,
            "path": obj.path,
            "GET": obj.GET.dict(),
            "POST": obj.POST.dict() if obj.method == "POST" else {},
            "headers": dict(obj.headers),
            "user": str(obj.user) if hasattr(obj, 'user') else None,
            "remote_ip": obj.META.get('REMOTE_ADDR'),
        }

    # Handle socket.socket (from runserver or dev tools)
    if isinstance(obj, socket):
        try:
            return {
                "type": "socket",
                "family": str(obj.family),
                "type_raw": str(obj.type),
                "proto": obj.proto,
                "sockname": obj.getsockname(),
                "remote_ip": obj.getpeername()[0],
            }
        except Exception as e:
            return {"type": "socket", "error": str(e)}

    # Default fallback: convert to string
    return str(obj)

class ExtendedJsonFormatter(JsonFormatter):
    def __init__(self, *args, json_default = json_default, json_encoder = None, json_serializer = json.dumps, json_indent = None, json_ensure_ascii = True, **kwargs):
        super().__init__(*args, json_default=json_default, json_encoder=json_encoder, json_serializer=json_serializer, json_indent=json_indent, json_ensure_ascii=json_ensure_ascii, **kwargs)
