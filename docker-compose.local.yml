volumes:
  swatmanga_local_postgres_data: {}
  swatmanga_local_postgres_data_backups: {}
  dbgate-data: {}


services:
  django:
    build:
      context: .
      dockerfile: ./compose/local/django/Dockerfile
    image: swatmanga_local_django
    container_name: swatmanga_server
    depends_on:
      - postgres
    volumes:
      - .:/app:z
      - /home/<USER>/media:/app/swatmanga/media
    env_file:
      - ./.envs/.local/.django
      - ./.envs/.local/.postgres
    ports:
      - '8000:8000'
    command: /start

  postgres:
    image: postgres:16
    container_name: swatmanga_db
    shm_size: 1g
    volumes:
      - swatmanga_local_postgres_data:/var/lib/postgresql/data
      - swatmanga_local_postgres_data_backups:/backups
    env_file:
      - ./.envs/.local/.postgres
    ports:
      - '5433:5432'

  redis:
    image: docker.io/redis:6
    container_name: swatmanga_redis

  dbgate:
    image: dbgate/dbgate
    restart: always
    env_file:
      - ./.envs/.local/.dbgate
    ports:
      - '3000:3000'
    volumes:
      - dbgate-data:/root/.dbgate
