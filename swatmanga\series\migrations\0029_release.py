# Generated by Django 5.0.8 on 2024-11-01 13:23

import django.contrib.postgres.fields
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("series", "0028_update_releases_view"),
    ]

    operations = [
        migrations.CreateModel(
            name="Release",
            fields=[
                ("serie_id", models.IntegerField(primary_key=True, serialize=False)),
                ("title", models.CharField(max_length=255)),
                ("latest_chapter_updated_at", models.DateTimeField()),
                ("slug", models.<PERSON>r<PERSON>ield(max_length=255)),
                ("type", models.<PERSON>r<PERSON>ield(max_length=100)),
                ("status", models.Char<PERSON>ield(max_length=100)),
                (
                    "genres",
                    django.contrib.postgres.fields.ArrayField(
                        base_field=models.CharField(blank=True, max_length=50),
                        size=None,
                    ),
                ),
                ("poster", models.ImageField(upload_to="")),
                ("is_hot", models.BooleanField()),
                ("views_count", models.IntegerField()),
                ("rating", models.DecimalField(decimal_places=2, max_digits=5)),
                (
                    "chapters",
                    django.contrib.postgres.fields.ArrayField(
                        base_field=models.JSONField(), size=None
                    ),
                ),
                ("created_at", models.DateTimeField()),
            ],
            options={
                "db_table": "releases",
                "managed": False,
            },
        ),
    ]
