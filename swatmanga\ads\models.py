from django.db import models

class GoogleAdLink(models.Model):
    PLATFORM_CHOICES = (
        ('web', 'Web'),
        ('android', 'Android'),
        ('ios', 'IOS'),
    )
    
    AD_TYPE_CHOICES = (
        ('app', 'App'),
        ('banner', 'Banner'),
        ('interstitial', 'Interstitial'),
        ('rewarded', 'Rewarded'),
        ('native', 'Native'),
        ('video', 'Video'),
        ('search', 'Search'),  # For text/search ads (Google Ads)
        # Add or remove choices according to your project needs.
    )
    
    platform = models.CharField(max_length=20, choices=PLATFORM_CHOICES)
    ad_type = models.CharField(max_length=20, choices=AD_TYPE_CHOICES)
    key = models.CharField(help_text="The key for the ad")
    
    def __str__(self):
        return f'{self.platform} - {self.ad_type}'
