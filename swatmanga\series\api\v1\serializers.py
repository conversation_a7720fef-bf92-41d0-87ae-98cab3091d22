import os

import zipfile
from django.conf import settings
from django.core.files.base import ContentFile
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from drf_spectacular.utils import extend_schema_serializer
from rest_framework import serializers
from rest_framework.exceptions import ValidationError
from natsort import natsorted

from swatmanga.common.api.serializers import CustomVersatileImageFieldSerializer
from swatmanga.series.models import (
    Chapter,
    ChapterImage,
    Donation,
    Genre,
    Release,
    Serie,
    SerieArtist,
    SerieAuthor,
    SerieStatus,
    SerieType,
)
from swatmanga.users.api.serializers import UserSerializer

from .utils import humanize_time_difference

class GenreIdsField(serializers.Field):
    default_error_messages = {
        'invalid': 'All genre IDs must be integers.',
        'does_not_exist': 'One or more genre IDs do not exist.'
    }

    def __init__(self, queryset, **kwargs):
        self.queryset = queryset
        super().__init__(**kwargs)

    def to_internal_value(self, data):
        # Convert input data to a list of integers
        try:
            if isinstance(data, str):
                ids = [int(item.strip()) for item in data.split(',')]
            elif isinstance(data, list):
                ids = []
                for item in data:
                    if isinstance(item, str) and ',' in item:
                        # Handle case where a list item contains a comma-separated string
                        ids.extend([int(i.strip()) for i in item.split(',')])
                    else:
                        ids.append(int(item))
            else:
                raise ValidationError(self.error_messages['invalid'])
        except (ValueError, TypeError):
            raise ValidationError(self.error_messages['invalid'])

        # Check if all genre IDs exist
        existing_ids = set(self.queryset.filter(id__in=ids).values_list('id', flat=True))
        if len(ids) != len(existing_ids):
            raise ValidationError(self.error_messages['does_not_exist'])

        return ids

    def to_representation(self, value):
        # Return list of genre IDs when serializing
        return [genre.id for genre in value.all()]

class SerieAuthorSerializer(serializers.ModelSerializer):
    class Meta:
        model = SerieAuthor
        fields = [
            "id",
            "name",
        ]


class SerieArtistSerializer(serializers.ModelSerializer):
    class Meta:
        model = SerieArtist
        fields = [
            "id",
            "name",
        ]


class SerieTypeSerializer(serializers.ModelSerializer):
    class Meta:
        model = SerieType
        fields = [
            "id",
            "name",
        ]
        extra_kwargs = {
            "url": {"view_name": "api:v1:serietype-list"},
        }


class SerieStatusSerializer(serializers.ModelSerializer):
    class Meta:
        model = SerieStatus
        fields = [
            "id",
            "name",
        ]
        extra_kwargs = {
            "url": {"view_name": "api:v1:seriestatus-list"},
        }


class GenreSerializer(serializers.ModelSerializer):
    class Meta:
        model = Genre
        fields = [
            "id",
            "name",
            "series_count",
        ]
        extra_kwargs = {
            "url": {"view_name": "api:v1:genre-list"},
        }


class SerieSerializerBase(serializers.ModelSerializer[Serie]):
    # TODO: add editor, translator fields
    poster = CustomVersatileImageFieldSerializer(sizes="series_poster", read_only=True)
    cover = CustomVersatileImageFieldSerializer(sizes="series_cover", read_only=True)
    type = SerieTypeSerializer(read_only=True)
    status = SerieStatusSerializer(read_only=True)
    genres = GenreSerializer(many=True, read_only=True)

    is_favorite = serializers.SerializerMethodField()
    is_followed = serializers.SerializerMethodField()
    rating = serializers.DecimalField(max_digits=3, decimal_places=1, default=7)

    def get_is_favorite(self, obj) -> bool:
        favorite_series_ids = self.context.get("favorite_series_ids", set())
        return obj.id in favorite_series_ids

    def get_is_followed(self, obj) -> bool:
        followed_series_ids = self.context.get("followed_series_ids", set())
        return obj.id in followed_series_ids


class SerieSerializer(SerieSerializerBase):
    class Meta:
        model = Serie
        fields = [
            "id",
            "title",
            "slug",
            "type",
            "type_id",
            "status",
            "status_id",
            "genres",
            "poster",
            "is_hot",
            "views_count",
            "rating",
            "is_favorite",
            "is_followed",
        ]
        extra_kwargs = {
            "url": {"view_name": "api:v1:serie-list"},
            "slug": {"read_only": True},
        }


class SerieDetailSerializer(SerieSerializerBase):
    # TODO: add my_rating field
    author_id = serializers.IntegerField(
        write_only=True,
        required=False,
        allow_null=True,
    )
    author = SerieAuthorSerializer(read_only=True)

    artist_id = serializers.IntegerField(
        write_only=True,
        required=False,
        allow_null=True,
    )
    artist = SerieArtistSerializer(read_only=True)

    status_id = serializers.IntegerField(
        write_only=True,
        required=True,
    )

    type_id = serializers.IntegerField(
        write_only=True,
        required=True,
    )

    genres_ids = GenreIdsField(queryset=Genre.objects.all(), write_only=True)

    created_at_humanized = serializers.SerializerMethodField()
    created_by = UserSerializer(read_only=True)
    updated_at_humanized = serializers.SerializerMethodField()
    updated_by = UserSerializer(read_only=True)

    class Meta:
        model = Serie
        fields = [
            "id",
            "title",
            "slug",
            "letter",
            "alternative",
            "story",
            "type",
            "type_id",
            "status",
            "status_id",
            "author",
            "author_id",
            "artist",
            "artist_id",
            "published",
            "genres",
            "genres_ids",
            "poster",
            "cover",
            "is_hot",
            "rating",
            "is_favorite",
            "is_followed",
            "chapters_count",
            "ratings_count",
            "views_count",
            "followers_count",
            "favorites_count",
            "donations_count",
            "allow_comments",
            "created_at",
            "created_at_humanized",
            "created_by",
            "updated_at",
            "updated_at_humanized",
            "updated_by",
        ]
        extra_kwargs = {
            "url": {"view_name": "api:v1:serie-detail", "lookup_field": "pk"},
            "slug": {"read_only": True},
            "letter": {"read_only": True},
        }

    def get_created_at_humanized(self, obj):
        return humanize_time_difference(obj.created_at, locale="ar")

    def get_updated_at_humanized(self, obj):
        return humanize_time_difference(obj.created_at, locale="ar")


    def create(self, validated_data):
        genres_ids = validated_data.pop("genres_ids", [])

        rating = validated_data.pop("rating", None)
        if rating is not None:
            validated_data["official_rating"] = rating

        validated_data["created_by"] = self.context["request"].user
        instance = super().create(validated_data)
        instance.genres.set(genres_ids)
        return instance

    def update(self, instance, validated_data):
        genres_ids = validated_data.pop("genres_ids", None)

        rating = validated_data.pop("rating", None)
        if rating is not None:
            validated_data["official_rating"] = rating

        validated_data["updated_by"] = self.context["request"].user
        instance = super().update(instance, validated_data)
        if genres_ids is not None:  # Only update if genres_ids was provided
            instance.genres.set(genres_ids)
        return instance


class SerieWriteSerializer(SerieDetailSerializer):
    poster = serializers.ImageField(write_only=True, required=True)
    cover = serializers.ImageField(write_only=True, required=False, allow_null=True)


class ChapterSerializer(serializers.ModelSerializer):
    created_at_humanized = serializers.SerializerMethodField()
    created_by = UserSerializer(read_only=True)
    updated_by = UserSerializer(read_only=True)

    class Meta:
        model = Chapter
        fields = [
            "id",
            "title",
            "slug",
            "chapter",
            "serie",
            "published",
            "views_count",
            "is_read",
            "created_at_humanized",
            "created_at",
            "created_by",
            "updated_at",
            "updated_by",
        ]
        extra_kwargs = {
            "url": {"view_name": "api:v1:chapter-list"},
            "slug": {"read_only": True},
        }

    def get_created_at_humanized(self, obj):
        return humanize_time_difference(obj.created_at, locale="ar")


class ChapterWithSerieSerializer(ChapterSerializer):
    serie = SerieSerializer(read_only=True)


class ChapterImageSerializer(serializers.ModelSerializer):
    class Meta:
        model = ChapterImage
        fields = ["image", "order"]


class ChapterWriteSerializer(serializers.ModelSerializer):
    zip_file = serializers.FileField(
        write_only=True, required=False, help_text=_("Zip file containing images")
    )
    serie = serializers.PrimaryKeyRelatedField(
        queryset=Serie.objects.all(), write_only=True, required=True
    )

    def validate_zip_file(self, zip_file):
        """Validate the zip file for supported image formats."""
        try:
            with zipfile.ZipFile(zip_file, "r") as z:
                invalid_files = [
                    f
                    for f in z.namelist()
                    if not f.endswith((".png", ".jpg", ".jpeg", ".webp"))
                ]
                if invalid_files:
                    raise ValidationError(
                        _("Zip file contains unsupported file types: %(files)s")
                        % {"files": ", ".join(invalid_files)}
                    )
                if not z.namelist():
                    raise ValidationError(_("Zip file is empty."))
        except zipfile.BadZipFile:
            raise ValidationError(_("Uploaded file is not a valid zip file."))
        return zip_file

    def create_chapter_images_from_zip(self, chapter, zip_file):
        with zipfile.ZipFile(zip_file, "r") as z:
            for i, filename in enumerate(natsorted(z.namelist())):
                ext = os.path.splitext(filename)[1]
                if ext.endswith(settings.ALLOWED_IMAGE_EXTENSIONS):
                    image_data = z.read(filename)
                    image_file = ContentFile(image_data, name=filename)
                    ChapterImage.objects.create(
                        chapter=chapter, image=image_file, order=i + 1
                    )

    def create(self, validated_data):
        images_data = validated_data.pop("images", [])
        zip_file = validated_data.pop("zip_file", None)

        # Validate that either images or zip_file is provided, but not both
        if images_data and zip_file:
            raise ValidationError(
                _("Please provide either a list of images or a zip file, not both.")
            )
        if not images_data and not zip_file:
            raise ValidationError(
                _("Please provide either a list of images or a zip file.")
            )

        validated_data["created_by"] = self.context["request"].user
        chapter = super().create(validated_data)

        # Handle images provided as a list
        if images_data:
            for i, image_data in enumerate(images_data):
                ChapterImage.objects.create(chapter=chapter, order=i + 1, **image_data)

        # Handle zip file
        if zip_file:
            zip_file = self.validate_zip_file(zip_file)
            self.create_chapter_images_from_zip(chapter, zip_file)

        return chapter

    def update(self, instance, validated_data):
        images_data = validated_data.pop("images", [])
        zip_file = validated_data.pop("zip_file", None)

        # Validate that either images or zip_file is provided, but not both
        if images_data and zip_file:
            raise ValidationError(
                _("Please provide either a list of images or a zip file, not both.")
            )
        if not images_data and not zip_file:
            raise ValidationError(
                _("Please provide either a list of images or a zip file.")
            )

        validated_data["updated_by"] = self.context["request"].user
        chapter = super().update(instance, validated_data)

        # Remove existing images before adding new ones
        instance.images.all().delete()

        # Handle images provided as a list
        if images_data:
            for i, image_data in enumerate(images_data):
                ChapterImage.objects.create(chapter=chapter, order=i + 1, **image_data)

        # Handle zip file
        if zip_file:
            zip_file = self.validate_zip_file(zip_file)
            self.create_chapter_images_from_zip(chapter, zip_file)

        return chapter

    class Meta:
        model = Chapter
        extra_kwargs = {
            "published": {"required": False, "allow_null": True},
        }
        fields = [
            "title",
            "chapter",
            "serie",
            "published",
            "zip_file",
        ]


class ChapterDetailSerializer(serializers.ModelSerializer):
    is_read = serializers.SerializerMethodField()
    created_by = UserSerializer(read_only=True)
    updated_by = UserSerializer(read_only=True)
    images = ChapterImageSerializer(many=True, required=False)

    next_chapter_id = serializers.SerializerMethodField()
    previous_chapter_id = serializers.SerializerMethodField()

    def get_is_read(self, obj) -> bool:
        return obj.is_read

    class Meta:
        model = Chapter
        fields = [
            "id",
            "title",
            "slug",
            "chapter",
            "serie",
            "published",
            "views_count",
            "next_chapter_id",
            "previous_chapter_id",
            "images_count",
            "is_read",
            "allow_comments",
            "created_at",
            "created_by",
            "updated_at",
            "updated_by",
            "images",
        ]
        extra_kwargs = {
            "url": {"view_name": "api:v1:chapter-detail", "lookup_field": "pk"},
            "slug": {"read_only": True},
        }

    def get_next_chapter_id(self, obj) -> int | None:
        return obj.next_chapter.id if obj.next_chapter else None

    def get_previous_chapter_id(self, obj) -> int | None:
        return obj.previous_chapter.id if obj.previous_chapter else None


class RatingActionSerializer(serializers.Serializer):
    rating = serializers.IntegerField(max_value=10, min_value=1)


@extend_schema_serializer(exclude_fields=["user", "serie"])
class DonationActionSerializer(serializers.ModelSerializer):
    class Meta:
        model = Donation
        fields = ["serie", "user", "coins"]
        extra_kwargs = {
            "user": {"required": False},
            "serie": {"required": False},
        }


class GeneralActionResponse(serializers.Serializer):
    details = serializers.CharField()


class ReleaseSerializer(serializers.ModelSerializer):
    poster = CustomVersatileImageFieldSerializer(
        sizes="series_poster",  # Reference the rendition key set defined in settings
        use_url=True,
    )

    class Meta:
        model = Release
        fields = [
            "serie_id",
            "title",
            "latest_chapter_updated_at",
            "slug",
            "type",
            "status",
            "genres",
            "poster",
            "is_hot",
            "views_count",
            "rating",
            "chapters",
        ]


class ChapterForReleasesSchemaSerializer(serializers.Serializer):
    id = serializers.IntegerField()
    title = serializers.CharField()
    chapter = serializers.CharField()


class ReleaseSchemaSerializer(serializers.Serializer):
    serie_id = serializers.IntegerField()
    title = serializers.CharField()
    latest_chapter_updated_at = serializers.DateTimeField()
    slug = serializers.CharField()
    type = SerieTypeSerializer()
    status = SerieStatusSerializer()
    genres = GenreSerializer(many=True)
    poster = serializers.CharField(allow_null=True)
    is_hot = serializers.BooleanField()
    views_count = serializers.IntegerField()
    rating = serializers.DecimalField(max_digits=5, decimal_places=2)
    chapters = ChapterForReleasesSchemaSerializer(many=True)
