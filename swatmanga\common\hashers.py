import bcrypt
from django.contrib.auth.hashers import BasePass<PERSON><PERSON>asher
from django.utils.encoding import force_bytes

class BcryptPasswordHasher(BasePasswordHasher):
    algorithm = "bcrypt"
    rounds = 10  # Match <PERSON><PERSON>'s configuration

    def salt(self):
        """Generate a proper bcrypt salt as bytes"""
        return bcrypt.gensalt(rounds=self.rounds)

    def encode(self, password, salt=None):
        """Handle both string and bytes inputs"""
        # Convert salt to bytes if provided as string
        if isinstance(salt, str):
            salt = salt.encode('utf-8')
            
        # Generate new salt if not provided
        if salt is None:
            salt = self.salt()
        
        # Ensure password is bytes
        password_bytes = force_bytes(password)
        
        # Hash with bcrypt
        hashed = bcrypt.hashpw(password_bytes, salt)
        
        # Return in Django-compatible format
        return f"{self.algorithm}${hashed.decode('utf-8')}"

    def verify(self, password, encoded):
        """Handle both prefixed and raw <PERSON><PERSON> hashes"""
        # Extract the actual bcrypt hash
        if encoded.startswith(f"{self.algorithm}$"):
            _, real_hash = encoded.split('$', 1)
        else:
            real_hash = encoded  # For existing Laravel hashes
            
        # Convert to bytes
        password_bytes = force_bytes(password)
        hash_bytes = force_bytes(real_hash)
        
        return bcrypt.checkpw(password_bytes, hash_bytes)

    def must_update(self, encoded):
        """Force update for Laravel-style hashes"""
        return encoded.startswith('$2y$') or not encoded.startswith(f"{self.algorithm}$")

    def safe_summary(self, encoded):
        return {
            'algorithm': self.algorithm,
            'hash': encoded.split('$', 1)[1] if '$' in encoded else encoded,
        }