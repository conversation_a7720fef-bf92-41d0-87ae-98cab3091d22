import argparse
from sqlalchemy import create_engine, inspect


# Function to extract the schema from a database (MySQL or PostgreSQL)
def extract_schema(db_url):
    # Create SQLAlchemy engine
    engine = create_engine(db_url)

    # Inspect the schema
    inspector = inspect(engine)
    tables = inspector.get_table_names()

    schema = {}
    for table_name in tables:
        columns = inspector.get_columns(table_name)
        schema[table_name] = columns

    # Output the schema
    for table, cols in schema.items():
        print(f"\nTable: {table}")
        for col in cols:
            print(f" - {col['name']} ({col['type']})")

    return schema


# Main function to handle command-line arguments and call schema extraction
def main():
    # Argument parser to handle command-line inputs
    parser = argparse.ArgumentParser(
        description="Extract schema from MySQL or PostgreSQL databases."
    )

    # Database connection URL argument
    parser.add_argument(
        "--db-url",
        required=True,
        help="The SQLAlchemy-compatible database URL (e.g., mysql+pymysql://user:pass@localhost/dbname or postgresql://user:pass@localhost/dbname).",
    )

    # Parse arguments
    args = parser.parse_args()

    # Extract schema based on the provided database type and URL
    schema = extract_schema(args.db_url)

    # Additional processing can go here (e.g., schema comparison, saving to a file, etc.)


if __name__ == "__main__":
    main()
