# Generated by Django 5.0.8 on 2024-08-13 11:34

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("series", "0003_serieartist_serieauthor_alter_serie_slug_and_more"),
    ]

    operations = [
        migrations.Alter<PERSON><PERSON>(
            model_name="serie",
            name="chapters_count",
            field=models.IntegerField(default=0, editable=False),
        ),
        migrations.AlterField(
            model_name="serie",
            name="donations_count",
            field=models.FloatField(default=0, editable=False),
        ),
        migrations.AlterField(
            model_name="serie",
            name="favorites_count",
            field=models.IntegerField(default=0, editable=False),
        ),
        migrations.Alter<PERSON>ield(
            model_name="serie",
            name="followers_count",
            field=models.IntegerField(default=0, editable=False),
        ),
        migrations.AlterField(
            model_name="serie",
            name="rating",
            field=models.DecimalField(
                decimal_places=1, default=0.0, editable=False, max_digits=2
            ),
        ),
        migrations.<PERSON><PERSON><PERSON><PERSON>(
            model_name="serie",
            name="ratings_count",
            field=models.IntegerField(default=0, editable=False),
        ),
        migrations.AlterField(
            model_name="serie",
            name="views_count",
            field=models.IntegerField(default=0, editable=False),
        ),
    ]
