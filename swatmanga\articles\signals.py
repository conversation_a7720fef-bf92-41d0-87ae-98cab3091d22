# signals.py
from django.db.models.signals import post_save, post_delete
from django.dispatch import receiver
from django.core.cache import cache
from django_redis import get_redis_connection
from .models import Article

@receiver([post_save, post_delete], sender=Article)
def invalidate_article_cache(sender, instance, **kwargs):
    
    # Invalidate list view cache (all versions)
    list_prefix = "GET:articles.list"
    keys = []
    keys+= cache.keys(f"*views.decorators.cache.cache_page.{list_prefix}*")
    
    # Invalidate specific article detail view
    retrieve_prefix = f"GET:articles.detail.{instance.pk}"
    keys+=cache.keys(f"*views.decorators.cache.cache_page.{retrieve_prefix}*")
    
    # Also clear any filter-based cache
    keys+=cache.keys("*articles*")

    if keys:
        cache.delete_many(keys=keys)
