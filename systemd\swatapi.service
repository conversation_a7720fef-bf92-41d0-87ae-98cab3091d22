[Unit]
Description=Swatmanga API daemon (instance %i)
After=network.target

[Service]
User=qubit
WorkingDirectory=/home/<USER>/swatmanga_backend
Environment=INSTANCE_NUMBER=%i
ExecStart=/home/<USER>/swatmanga_backend/.venv/bin/gunicorn \
          --access-logfile - \
          --timeout 240 \
          --workers 3 \
          --bind 127.0.0.1:800%i \
          config.wsgi:application

[Install]
WantedBy=multi-user.target