import factory
import faker as fk
from django.contrib.contenttypes.models import ContentType
from django.utils.text import slugify
from factory import Faker, post_generation
from factory.django import DjangoModelFactory, ImageField
from hitcount.models import HitCount

from swatmanga.series.models import (
    Chapter,
    ChapterImage,
    ChapterRead,
    Donation,
    Favorite,
    Follow,
    Genre,
    Rating,
    Serie,
    SerieArtist,
    SerieAuthor,
    SerieStatus,
    SerieType,
)
from swatmanga.users.factories import UserFactory


faker = fk.Faker()


class SerieAuthorFactory(DjangoModelFactory):
    class Meta:
        model = SerieAuthor
        django_get_or_create = ("name",)

    name = Faker("name")


class SerieArtistFactory(DjangoModelFactory):
    class Meta:
        model = SerieArtist
        django_get_or_create = ("name",)

    name = Faker("name")


class SerieTypeFactory(DjangoModelFactory):
    class Meta:
        model = SerieType
        django_get_or_create = ("name",)

    name = Faker("word")


class SerieStatusFactory(DjangoModelFactory):
    class Meta:
        model = SerieStatus
        django_get_or_create = ("name",)

    name = Faker("word")


class GenreFactory(DjangoModelFactory):
    class Meta:
        model = Genre
        django_get_or_create = ("name",)

    name = Faker("word")


class SerieFactory(DjangoModelFactory):
    class Meta:
        model = Serie
        django_get_or_create = ("slug",)

    title = Faker("sentence", nb_words=3)
    slug = factory.LazyAttribute(lambda o: slugify(o.title))
    story = Faker("paragraph", nb_sentences=5)
    author = factory.SubFactory(SerieAuthorFactory)
    artist = factory.SubFactory(SerieArtistFactory)
    type = factory.SubFactory(SerieTypeFactory)
    status = factory.SubFactory(SerieStatusFactory)
    published = Faker("date_this_decade")

    @post_generation
    def genres(self, create, extracted, **kwargs):
        if not create:
            return
        if extracted:
            for genre in extracted:
                self.genres.add(genre)

    poster = ImageField(filename="poster.webp", from_path="samples/sample_poster.webp")
    cover = ImageField(filename="cover.webp", from_path="samples/sample_cover.webp")

    @post_generation
    def hit_count_generic(self, create, extracted, **kwargs):
        if not create:
            return
        # Save the serie before creating HitCount
        if extracted:
            content_type = ContentType.objects.get_for_model(self)
            HitCount.objects.create(
                content_type=content_type, object_pk=self.pk, hits=0
            )


class RatingFactory(DjangoModelFactory):
    class Meta:
        model = Rating
        django_get_or_create = ("serie", "user")

    serie = factory.SubFactory(SerieFactory)
    user = factory.SubFactory(UserFactory)
    rating = factory.Iterator([1, 2, 3, 4, 5, 6, 7, 8, 9, 10])


class DonationFactory(DjangoModelFactory):
    class Meta:
        model = Donation
        django_get_or_create = ("serie", "user")

    serie = factory.SubFactory(SerieFactory)
    user = factory.SubFactory(UserFactory)
    coins = Faker("random_int", min=1, max=100)


class FollowFactory(DjangoModelFactory):
    class Meta:
        model = Follow
        django_get_or_create = ("serie", "user")

    serie = factory.SubFactory(SerieFactory)
    user = factory.SubFactory(UserFactory)


class FavoriteFactory(DjangoModelFactory):
    class Meta:
        model = Favorite
        django_get_or_create = ("serie", "user")

    serie = factory.SubFactory(SerieFactory)
    user = factory.SubFactory(UserFactory)


class ChapterFactory(DjangoModelFactory):
    class Meta:
        model = Chapter
        django_get_or_create = ("chapter", "serie")

    title = Faker("sentence", nb_words=3)
    slug = factory.LazyAttribute(lambda o: slugify(o.title))
    chapter = Faker("random_number", digits=3)
    serie = factory.SubFactory(SerieFactory)

    @post_generation
    def hit_count_generic(self, create, extracted, **kwargs):
        if not create:
            return
        # Save the serie before creating HitCount
        if extracted:
            content_type = ContentType.objects.get_for_model(self)
            HitCount.objects.create(
                content_type=content_type, object_pk=self.pk, hits=0
            )

    @factory.post_generation
    def created_by(self, create, extracted, **kwargs):
        if not create:
            return
        if extracted:
            self.created_by = extracted


class ChapterImageFactory(DjangoModelFactory):
    class Meta:
        model = ChapterImage
        django_get_or_create = ("chapter", "order")

    chapter = factory.SubFactory(ChapterFactory)
    order = Faker("random_int", min=1, max=100)
    image = ImageField(
        filename="chapter_image.webp", from_path="samples/sample_chapter.webp"
    )  # Using ImageField to generate image


class ChapterReadFactory(DjangoModelFactory):
    class Meta:
        model = ChapterRead
        django_get_or_create = ("chapter", "user")

    chapter = factory.SubFactory(ChapterFactory)
    user = factory.SubFactory(UserFactory)
