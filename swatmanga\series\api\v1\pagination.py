from rest_framework.pagination import CursorPagination, PageNumberPagination
from rest_framework.response import Response


class NoCountPageNumberPagination(PageNumberPagination):
    max_page_size = 200

    def get_paginated_response(self, data):
        return Response(
            {
                "next": self.get_next_link(),
                "previous": self.get_previous_link(),
                "results": data,
            }
        )


class ChapterCursorPagination(CursorPagination):
    ordering = "updated_at"
    page_size = 20
    max_page_size = 200
