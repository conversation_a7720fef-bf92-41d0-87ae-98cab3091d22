# Generated by Django 5.0.8 on 2024-11-01 10:09

from django.db import migrations


def reverse_create_materialized_view(apps, schema_editor):
    schema_editor.execute(
        """
            DROP MATERIALIZED VIEW IF EXISTS releases;
            DROP INDEX IF EXISTS idx_releases_serie_id;
            DROP INDEX IF EXISTS idx_latest_chapter_updated_at;
        """
    )


def create_materialized_view(apps, schema_editor):
    schema_editor.execute(
        """
        create materialized view releases AS
        WITH ranked_series AS (
            SELECT
                s.id AS serie_id,
                s.title,
                s.latest_chapter_updated_at,
                s.slug,
                st.name AS type,
                ss.name AS status,
                ARRAY_AGG(g.name) AS genres,
                s.poster,
                s.is_hot,
                s.views_count,
                s.rating
            FROM
                series s
            LEFT JOIN serie_types st ON s.type_id = st.id
            LEFT JOIN serie_statuses ss ON s.status_id = ss.id
            LEFT JOIN series_genres sg ON s.id = sg.serie_id
            LEFT JOIN genres g ON sg.genre_id = g.id
            WHERE
                s.latest_chapter_updated_at IS NOT NULL
            GROUP BY
                s.id, st.name, ss.name
            ORDER BY
                s.latest_chapter_updated_at DESC
            LIMIT 200
        )
        SELECT
            rs.serie_id,
            rs.title,
            rs.latest_chapter_updated_at,
            rs.slug,
            rs.type,
            rs.status,
            rs.genres,
            rs.poster,
            rs.is_hot,
            rs.views_count,
            rs.rating,
            c.id AS chapter_id,
            c.title AS chapter_title,
            c.created_at
        FROM
            ranked_series rs
        LEFT JOIN LATERAL (
            SELECT
                c.id,
                c.title,
                c.created_at
            FROM
                chapters c
            WHERE
                c.serie_id = rs.serie_id
            ORDER BY
                c.created_at DESC
            LIMIT 4
        ) c ON true;
    CREATE INDEX IF NOT EXISTS idx_releases_serie_id ON releases (serie_id);
    CREATE INDEX IF NOT EXISTS idx_releases_latest_chapter_updated_at ON releases (latest_chapter_updated_at);
    """
    )


class Migration(migrations.Migration):

    dependencies = [
        ("series", "0026_serie_latest_chapter_updated_at"),
    ]

    operations = [
        migrations.RunPython(
            create_materialized_view, reverse_code=reverse_create_materialized_view
        )
    ]
