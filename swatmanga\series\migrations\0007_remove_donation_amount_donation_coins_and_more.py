# Generated by Django 5.0.8 on 2024-08-15 13:48

import django.core.validators
from django.db import migrations, models

import swatmanga.series.models


class Migration(migrations.Migration):
    dependencies = [
        ("series", "0006_chapterread_chapter_serie_chapter_number_uniq_and_more"),
    ]

    operations = [
        migrations.RemoveField(
            model_name="donation",
            name="amount",
        ),
        migrations.AddField(
            model_name="donation",
            name="coins",
            field=models.IntegerField(default=0),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name="chapterimage",
            name="image",
            field=swatmanga.series.models.WebPVersatileImageField(
                blank=True,
                null=True,
                upload_to=swatmanga.series.models.chapter_image_upload_to,
                validators=[
                    django.core.validators.FileExtensionValidator(
                        allowed_extensions=[".jpeg", ".jpg", ".webp", ".png", ".gif"]
                    )
                ],
                verbose_name="Chapter Image",
            ),
        ),
        migrations.<PERSON>er<PERSON>ield(
            model_name="serie",
            name="cover",
            field=swatmanga.series.models.WebPVersatileImageField(
                blank=True,
                null=True,
                upload_to=swatmanga.series.models.serie_cover_upload_to,
                validators=[
                    django.core.validators.FileExtensionValidator(
                        allowed_extensions=[".jpeg", ".jpg", ".webp", ".png", ".gif"]
                    )
                ],
                verbose_name="Cover Image",
            ),
        ),
    ]
