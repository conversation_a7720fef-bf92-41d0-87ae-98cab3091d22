import json
import logging


logger = logging.getLogger("django.request")


class RequestLoggingMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        # Log request headers and body
        body_data = ""
        if request.method in [
            "POST",
            "PUT",
            "PATCH",
        ]:  # Methods that usually have bodies
            try:
                body = request.body.decode("utf-8")
                body_data = json.loads(body) if body else {}
            except (ValueError, UnicodeDecodeError):
                body_data = ""

        headers = {k: v for k, v in request.headers.items()}

        logger.info(
            json.dumps(
                {
                    "path": request.path,
                    "method": request.method,
                    "headers": headers,
                    "body": body_data,
                }
            )
        )

        response = self.get_response(request)
        return response
