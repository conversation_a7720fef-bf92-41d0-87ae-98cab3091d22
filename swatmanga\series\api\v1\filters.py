from datetime import timedelta
from django.db.models import F, Sum
from django.utils import timezone
from django_filters import rest_framework as filters
from hitcount.models import Hit

from swatmanga.series.models import (
    Chapter,
    Genre,
    Serie,
    SerieArtist,
    SerieAuthor,
    SerieStatus,
    SerieType,
)


class SerieFilterSet(filters.FilterSet):
    # Filter by foreign key fields
    type = filters.ModelMultipleChoiceFilter(queryset=SerieType.objects.all())
    status = filters.ModelMultipleChoiceFilter(queryset=SerieStatus.objects.all())
    author = filters.ModelChoiceFilter(queryset=SerieAuthor.objects.all())
    artist = filters.ModelChoiceFilter(queryset=SerieArtist.objects.all())

    # Filter by many-to-many field
    genres = filters.ModelMultipleChoiceFilter(queryset=Genre.objects.all())

    # Filter by boolean field
    is_hot = filters.BooleanFilter()

    # Search by title
    search = filters.CharFilter(field_name="title", lookup_expr="icontains")

    letter = filters.CharFilter(field_name="letter")

    order_by = filters.OrderingFilter(
        fields=(
            ("title", "title"),
            ("rating", "rating"),
            ("followers_count", "followers_count"),
            ("views_count", "views_count"),
            ("chapters_count", "chapters_count"),
            ("created_at", "created_at"),
        ),
    )

    class Meta:
        model = Serie
        fields = [
            "type",
            "status",
            "genres",
            "is_hot",
            "search",
            "author",
            "artist",
        ]


TIMEFRAME_OPTIONS = {
    "day": timedelta(days=1),
    "3_days": timedelta(days=3),
    "week": timedelta(weeks=1),
    "month": timedelta(days=30),
}


class ChapterFilterSet(filters.FilterSet):

    TIME_CHOICES = [
        (key, key.replace("_", " ").title()) for key in TIMEFRAME_OPTIONS.keys()
    ]

    created_last = filters.ChoiceFilter(
        choices=TIME_CHOICES, method="filter_by_created_at"
    )

    views_timeframe = filters.ChoiceFilter(
        choices=TIME_CHOICES, method="filter_by_views_timeframe"
    )

    is_read = filters.BooleanFilter(
        method="filter_is_read",
        label="Read Status",
        help_text="Filter chapters by read/unread status. if user's not authenticated this has no effect",
    )
    serie = filters.ModelChoiceFilter(queryset=Serie.objects.all())

    order_by = filters.OrderingFilter(
        fields=(
            ("order", "order"),
            ("views_count", "views_count"),
            ("updated_at", "updated_at"),
            ("created_at", "created_at"),
        ),
    )

    class Meta:
        model = Chapter
        fields = ["is_read", "views_timeframe", "created_last", "serie"]

    def filter_is_read(self, queryset, name, value):
        request = self.request
        if request is not None and request.user.is_authenticated:
            user = request.user
            if value:  # When is_read is True, return the chapters the user has read
                return queryset.filter(chapterreads__user=user)
            else:  # When is_read is False, return the chapters the user hasn't read
                return queryset.exclude(chapterreads__user=user)
        # If the user is not authenticated, return the original queryset
        return queryset

    def filter_order_by_read_at(self, queryset, name, value):
        request = self.request
        if request is not None and request.user.is_authenticated:
            user = request.user
            if "read_at" in value:
                # Order by the time the user read the chapter
                return queryset.filter(chapterreads__user=user).order_by(
                    "chapterreads__read_at"
                )
            elif "-read_at" in value:
                # Order by the time the user read the chapter in descending order
                return queryset.filter(chapterreads__user=user).order_by(
                    "-chapterreads__read_at"
                )
        # If no ordering by read_at is applied or user is not authenticated, return the original queryset
        return queryset

    def filter_by_created_at(self, queryset, name, value):

        time_delta = TIMEFRAME_OPTIONS.get(value)
        if not time_delta:
            return queryset

        # Calculate the time threshold
        time_threshold = timezone.now() - time_delta

        return queryset.filter(created_at__gte=time_threshold)

    def filter_by_views_timeframe(self, queryset, name, value):

        time_delta = TIMEFRAME_OPTIONS.get(value)
        if not time_delta:
            return queryset

        # Calculate the time threshold
        time_threshold = timezone.now() - time_delta

        hit_counts = (
            Hit.objects.filter(
                created__gte=time_threshold  # Only consider hits within the time period
            )
            .values("hitcount__object_pk")
            .annotate(total_hits=Sum("hitcount__hits"))  # Sum the hits for each chapter
        )

        # Create a dictionary mapping chapter ID to total hits
        chapter_hit_map = {
            hit["hitcount__object_pk"]: hit["total_hits"] for hit in hit_counts
        }

        # Annotate the Chapter queryset with the total hits for each chapter
        queryset = queryset.annotate(total_hits=F("hit_count_generic__hits"))

        # Order the queryset by the computed total hits in descending order
        queryset = queryset.filter(id__in=chapter_hit_map.keys()).order_by(
            F("total_hits").desc()
        )

        return queryset


class SerieAuthorFilterSet(filters.FilterSet):
    search = filters.CharFilter(field_name="name", lookup_expr="icontains")
    class Meta:
        model = SerieAuthor
        fields = [
            "search",
        ]


class SerieArtistFilterSet(filters.FilterSet):
    search = filters.CharFilter(field_name="name", lookup_expr="icontains")
    class Meta:
        model = SerieArtist
        fields = [
            "search",
        ]

class GenreFilterSet(filters.FilterSet):
    search = filters.CharFilter(field_name="name", lookup_expr="icontains")
    class Meta:
        model = Genre
        fields = [
            "search",
        ]

