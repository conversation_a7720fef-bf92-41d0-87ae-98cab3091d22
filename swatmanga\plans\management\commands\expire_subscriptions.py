# management/commands/expire_subscriptions.py
from django.core.management.base import BaseCommand
from django.utils import timezone
from swatmanga.plans.models import PlanSubscription, Plan

class Command(BaseCommand):
    help = "Expire subscription plans that have reached their end date"

    def handle(self, *args, **options):
        now = timezone.now()
        expired = PlanSubscription.objects.filter(
            plan__plan_type=Plan.PlanType.SUBSCRIPTION,
            expiration_date__lte=now,
            status=PlanSubscription.PlanSubscriptionStatus.SUBSCRIBED
        ).update(status=PlanSubscription.PlanSubscriptionStatus.EXPIRED)
        
        self.stdout.write(f"Expired {expired} subscriptions")