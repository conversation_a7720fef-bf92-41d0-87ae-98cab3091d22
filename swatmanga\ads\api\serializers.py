from rest_framework import serializers
from ..models import GoogleAdLink


class GoogleAdLinkSerializer(serializers.ModelSerializer):
    class Meta:
        model = GoogleAdLink
        fields = ("platform", "ad_type", "key")


from rest_framework import serializers


class GroupedAdSerializer(serializers.Serializer):
    type = serializers.CharField(
        help_text="The type of the ad",
    )
    key = serializers.URLField(
        help_text="The key for the ad",
    )


class GoogleAdsGroupedSchemaSerializer(serializers.Serializer):
    """
    A serializer solely for schema generation which describes the
    structure of the grouped Google Ads output.
    """

    web = GroupedAdSerializer(
        required=False, help_text="Ad configuration for web", many=True
    )
    android = GroupedAdSerializer(
        required=False, help_text="Ad configuration for Android", many=True
    )
    ios = GroupedAdSerializer(
        required=False, help_text="Ad configuration for IOS", many=True
    )

