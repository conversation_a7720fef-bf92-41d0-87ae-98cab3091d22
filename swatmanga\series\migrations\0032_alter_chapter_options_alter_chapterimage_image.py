# Generated by Django 5.0.8 on 2024-11-29 16:09

import django.core.validators
from django.db import migrations

import swatmanga.common.fields
import swatmanga.series.models


class Migration(migrations.Migration):

    dependencies = [
        ("series", "0031_alter_genre_series_count"),
    ]

    operations = [
        migrations.AlterModelOptions(
            name="chapter",
            options={"ordering": ["-created_at"]},
        ),
        migrations.AlterField(
            model_name="chapterimage",
            name="image",
            field=swatmanga.common.fields.WebPVersatileImageField(
                blank=True,
                max_length=255,
                null=True,
                upload_to=swatmanga.series.models.chapter_image_upload_to,
                validators=[
                    django.core.validators.FileExtensionValidator(
                        allowed_extensions=["jpeg", "jpg", "webp", "png", "gif"]
                    )
                ],
                verbose_name="Chapter Image",
            ),
        ),
    ]
