from rest_framework import serializers
from versatileimagefield.serializers import VersatileImageFieldSerializer
from drf_spectacular.utils import extend_schema_field, OpenApiTypes


@extend_schema_field(OpenApiTypes.OBJECT)
class CustomVersatileImageFieldSerializer(VersatileImageFieldSerializer):
    """Return an object of images with different sizes"""

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

    def to_representation(self, value):
        try:
            return super().to_representation(value)
        except FileNotFoundError:
            return {
                "medium": "https://appswat.com/v2/media/__sized__/2024/03/46d88-77ffb-ee44d-crop-c0-5__0-5-800x1200-70.webp",
                "thumbnail": "https://appswat.com/v2/media/__sized__/2024/03/46d88-77ffb-ee44d-crop-c0-5__0-5-800x1200-70.webp",
                "small": "https://appswat.com/v2/media/__sized__/2024/03/46d88-77ffb-ee44d-crop-c0-5__0-5-800x1200-70.webp",
            }
